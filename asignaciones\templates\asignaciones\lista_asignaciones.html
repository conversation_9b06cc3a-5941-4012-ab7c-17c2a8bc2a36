{% extends 'Base/base.html' %}
{% load static %}

{% block title %}Gestión de Asignaciones{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header con estadísticas -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div class="avatar-lg me-3">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div>
                                <h4 class="mb-1">Gestión de Asignaciones</h4>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-chart-bar me-1"></i>{{ stats.total_asignaciones }} asignación{{ stats.total_asignaciones|pluralize:"es" }}
                                    {% if stats.tickets_sin_asignar > 0 %}
                                        <span class="ms-3 text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>{{ stats.tickets_sin_asignar }} sin asignar
                                        </span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="text-end">
                            {% if puede_gestionar %}
                                <a href="{% url 'tickets:lista_tickets' %}" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-ticket-alt me-2"></i>Ver Tickets
                                </a>
                                <a href="{% url 'asignaciones:reportes_asignaciones' %}" class="btn btn-success">
                                    <i class="fas fa-chart-line me-2"></i>Reportes
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas rápidas -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-primary mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h4 class="text-primary">{{ stats.tickets_sin_asignar }}</h4>
                    <small class="text-muted">Sin Asignar</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                    <h4 class="text-info">{{ stats.asignaciones_activas }}</h4>
                    <small class="text-muted">Activas</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h4 class="text-success">{{ stats.asignaciones_finalizadas }}</h4>
                    <small class="text-muted">Finalizadas</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-list fa-2x"></i>
                    </div>
                    <h4 class="text-warning">{{ stats.total_asignaciones }}</h4>
                    <small class="text-muted">Total</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros avanzados -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="q" class="form-label">Buscar</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="q" name="q" 
                                       value="{{ filtros.busqueda }}" 
                                       placeholder="Ticket, usuario...">
                            </div>
                        </div>
                        
                        {% if es_administrador %}
                            <div class="col-md-2">
                                <label for="area" class="form-label">Área</label>
                                <select class="form-select" id="area" name="area">
                                    <option value="">Todas las áreas</option>
                                    {% for area in areas_disponibles %}
                                        <option value="{{ area.id }}" 
                                                {% if area.id|stringformat:"s" == filtros.area_id %}selected{% endif %}>
                                            {{ area.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        {% endif %}
                        
                        <div class="col-md-2">
                            <label for="estado" class="form-label">Estado</label>
                            <select class="form-select" id="estado" name="estado">
                                <option value="">Todos</option>
                                <option value="1" {% if filtros.estado == "1" %}selected{% endif %}>Asignado</option>
                                <option value="2" {% if filtros.estado == "2" %}selected{% endif %}>En Progreso</option>
                                <option value="3" {% if filtros.estado == "3" %}selected{% endif %}>Finalizado</option>
                                <option value="4" {% if filtros.estado == "4" %}selected{% endif %}>Cancelado</option>
                            </select>
                        </div>
                        
                        {% if puede_gestionar %}
                            <div class="col-md-2">
                                <label for="usuario" class="form-label">Usuario</label>
                                <select class="form-select" id="usuario" name="usuario">
                                    <option value="">Todos</option>
                                    {% for usuario in usuarios_disponibles %}
                                        <option value="{{ usuario.id }}" 
                                                {% if usuario.id|stringformat:"s" == filtros.usuario_id %}selected{% endif %}>
                                            {{ usuario.get_full_name|default:usuario.username }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        {% endif %}
                        
                        <div class="col-md-2">
                            <label class="form-label">Opciones</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sin_asignar" name="sin_asignar" 
                                       value="true" {% if filtros.mostrar_sin_asignar %}checked{% endif %}>
                                <label class="form-check-label" for="sin_asignar">
                                    <small>Mostrar sin asignar</small>
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de asignaciones -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        {% if puede_gestionar %}
                            Asignaciones y Tickets Pendientes
                        {% else %}
                            Mis Asignaciones
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if items %}
                        <div id="asignaciones-container">
                            {% include 'asignaciones/parciales/lista_asignaciones.html' %}
                        </div>

                        <!-- Loading indicator -->
                        <div id="loading-indicator" class="text-center py-3" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                            <p class="mt-2 text-muted">Cargando más asignaciones...</p>
                        </div>

                        <!-- Paginación -->
                        {% if items.has_next %}
                            <div class="card-footer bg-white border-top">
                                <div class="text-center">
                                    <button id="load-more" class="btn btn-outline-primary" data-page="{{ items.next_page_number }}">
                                        <i class="fas fa-chevron-down me-2"></i>Cargar más
                                    </button>
                                </div>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No hay asignaciones</h5>
                            <p class="text-muted">
                                {% if filtros.busqueda %}
                                    No se encontraron asignaciones con el criterio "{{ filtros.busqueda }}"
                                {% else %}
                                    No hay asignaciones disponibles en este momento
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para asignar ticket mejorado -->
<div class="modal fade" id="modalAsignarTicket" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>Asignar Ticket
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="form-asignar-ticket">
                {% csrf_token %}
                <div class="modal-body">
                    <!-- Información del ticket -->
                    <div class="alert alert-info mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-ticket-alt fa-2x me-3"></i>
                            <div>
                                <h6 id="ticket-info-titulo" class="mb-1"></h6>
                                <small id="ticket-info-id" class="text-muted"></small>
                            </div>
                        </div>
                    </div>

                    <!-- Buscador de usuarios -->
                    <div class="mb-3">
                        <label for="buscar-usuario-asignacion" class="form-label">
                            <i class="fas fa-search me-1"></i>Buscar Usuario
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="buscar-usuario-asignacion"
                                   placeholder="Buscar por nombre, email o área..." autocomplete="off">
                            <button type="button" class="btn btn-outline-secondary" id="limpiar-busqueda-asignacion">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="form-text">Escriba para filtrar la lista de usuarios disponibles</div>
                    </div>

                    <!-- Lista de usuarios -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-users me-1"></i>Usuarios Disponibles
                            <span class="badge bg-primary ms-2" id="contador-usuarios-asignacion">{{ usuarios_disponibles|length }}</span>
                        </label>
                        <div class="border rounded" style="max-height: 300px; overflow-y: auto;" id="lista-usuarios-asignacion">
                            {% for usuario in usuarios_disponibles %}
                                <div class="usuario-item-asignacion p-3 border-bottom" data-usuario-id="{{ usuario.id }}"
                                     data-nombre="{{ usuario.get_full_name|default:usuario.username|lower }}"
                                     data-email="{{ usuario.email|lower }}"
                                     data-areas="{% for grupo in usuario.groups.all %}{{ grupo.name|lower }} {% endfor %}">
                                    <div class="d-flex align-items-center">
                                        <div class="form-check me-3">
                                            <input class="form-check-input" type="radio" name="usuario_seleccionado_asignacion"
                                                   value="{{ usuario.id }}" id="usuario_asignacion_{{ usuario.id }}">
                                        </div>
                                        <div class="avatar-sm me-3">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">{{ usuario.get_full_name|default:usuario.username }}</h6>
                                            <div class="d-flex align-items-center text-sm">
                                                {% if usuario.email %}
                                                    <span class="text-muted me-3">
                                                        <i class="fas fa-envelope me-1"></i>{{ usuario.email }}
                                                    </span>
                                                {% endif %}
                                                {% if es_administrador %}
                                                    <div class="areas-usuario">
                                                        {% for grupo in usuario.groups.all %}
                                                            <span class="badge bg-secondary me-1">{{ grupo.name }}</span>
                                                        {% endfor %}
                                                    </div>
                                                {% endif %}
                                            </div>
                                           
                                        </div>
                                        <div class="text-end">
                                            <button type="button" class="btn btn-sm btn-outline-warning seleccionar-usuario-asignacion"
                                                    data-usuario-id="{{ usuario.id }}"
                                                    data-usuario-nombre="{{ usuario.get_full_name|default:usuario.username }}">
                                                <i class="fas fa-check me-1"></i>Seleccionar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            {% empty %}
                                <div class="text-center py-4 text-muted">
                                    <i class="fas fa-users-slash fa-2x mb-2"></i>
                                    <p>No hay usuarios disponibles para asignar</p>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Usuario seleccionado -->
                    <div id="usuario-seleccionado-asignacion" class="alert alert-warning" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="alert-heading mb-1">
                                    <i class="fas fa-user-check me-2"></i>Usuario Seleccionado
                                </h6>
                                <p class="mb-0" id="info-usuario-seleccionado-asignacion"></p>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removerSeleccionAsignacion()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Nota opcional -->
                    <div class="mb-3">
                        <label for="nota_asignacion" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>Nota de Asignación <span class="text-muted">(Opcional)</span>
                        </label>
                        <textarea class="form-control" id="nota_asignacion" name="nota" rows="3"
                                  placeholder="Instrucciones especiales, prioridades o comentarios para el usuario asignado..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="submit" class="btn btn-warning" id="btn-asignar" disabled>
                        <i class="fas fa-user-plus me-2"></i>Asignar Ticket
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, #6f42c1, #e83e8c);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.card {
    transition: transform 0.1s ease;
}

.card:hover {
    transform: translateY(-2px);
}

#loading-indicator {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Estilos para modales mejorados */
.usuario-item-asignacion:hover {
    background-color: #f8f9fa;
}

.usuario-item-asignacion.selected {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.avatar-sm {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(45deg, #17a2b8, #138496);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.areas-usuario .badge {
    font-size: 0.7em;
}

.form-check-input:checked {
    background-color: #ffc107;
    border-color: #ffc107;
}

.seleccionar-usuario-asignacion:hover {
    transform: scale(1.05);
}

.alert-warning {
    border-left: 4px solid #ffc107;
}

.alert-success {
    border-left: 4px solid #28a745;
}

/* Estilos para tickets con múltiples asignaciones */
.ticket-con-asignaciones {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.ticket-con-asignaciones:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
    transform: translateX(2px);
    transition: all 0.2s ease;
}

.ticket-con-asignaciones .avatar-sm {
    background: linear-gradient(45deg, #6f42c1, #e83e8c);
}

.ticket-con-asignaciones .badge {
    font-size: 0.75em;
    font-weight: 500;
}

.ticket-con-asignaciones .dropdown-menu {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border: none;
}

.ticket-con-asignaciones .dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
    transition: all 0.1s ease;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loadMoreBtn = document.getElementById('load-more');
    const loadingIndicator = document.getElementById('loading-indicator');
    const asignacionesContainer = document.getElementById('asignaciones-container');
    
    // Scroll infinito
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            const page = this.dataset.page;
            const url = new URL(window.location);
            url.searchParams.set('page', page);
            
            // Mostrar loading
            loadingIndicator.style.display = 'block';
            this.style.display = 'none';
            
            fetch(url.toString(), {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Agregar nuevas asignaciones
                asignacionesContainer.insertAdjacentHTML('beforeend', data.html);
                
                // Ocultar loading
                loadingIndicator.style.display = 'none';
                
                // Actualizar botón o ocultarlo
                if (data.has_next) {
                    this.dataset.page = parseInt(page) + 1;
                    this.style.display = 'block';
                } else {
                    this.parentElement.parentElement.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                loadingIndicator.style.display = 'none';
                this.style.display = 'block';
                alert('Error al cargar más asignaciones');
            });
        });
    }

    // Configurar formulario de asignación
    const formAsignar = document.getElementById('form-asignar-ticket');
    if (formAsignar) {
        formAsignar.addEventListener('submit', function(e) {
            e.preventDefault();
            enviarAsignacion();
        });
    }

    // Configurar buscador del modal
    const buscadorAsignacion = document.getElementById('buscar-usuario-asignacion');
    if (buscadorAsignacion) {
        buscadorAsignacion.addEventListener('input', function() {
            filtrarUsuariosAsignacion(this.value);
        });
    }

    // Configurar botón limpiar búsqueda
    const limpiarBtnAsignacion = document.getElementById('limpiar-busqueda-asignacion');
    if (limpiarBtnAsignacion) {
        limpiarBtnAsignacion.addEventListener('click', function() {
            buscadorAsignacion.value = '';
            filtrarUsuariosAsignacion('');
        });
    }

    // Configurar botones de selección
    document.querySelectorAll('.seleccionar-usuario-asignacion').forEach(btn => {
        btn.addEventListener('click', function() {
            const usuarioId = this.dataset.usuarioId;
            const usuarioNombre = this.dataset.usuarioNombre;
            seleccionarUsuarioAsignacion(usuarioId, usuarioNombre);
        });
    });

    // Configurar radios
    document.querySelectorAll('input[name="usuario_seleccionado_asignacion"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                const usuarioItem = this.closest('.usuario-item-asignacion');
                const usuarioId = this.value;
                const usuarioNombre = usuarioItem.querySelector('h6').textContent;
                seleccionarUsuarioAsignacion(usuarioId, usuarioNombre);
            }
        });
    });
});

// Variables globales para el modal
let ticketIdActual = null;
let usuarioSeleccionadoIdAsignacion = null;

function mostrarModalAsignar(ticketId, ticketTitulo) {
    ticketIdActual = ticketId;
    usuarioSeleccionadoIdAsignacion = null;

    // Actualizar información del ticket en el modal
    document.getElementById('ticket-info-titulo').textContent = ticketTitulo;
    document.getElementById('ticket-info-id').textContent = `Ticket #${ticketId}`;

    // Limpiar formulario y selecciones
    document.getElementById('buscar-usuario-asignacion').value = '';
    document.getElementById('nota_asignacion').value = '';
    document.getElementById('usuario-seleccionado-asignacion').style.display = 'none';
    document.getElementById('btn-asignar').disabled = true;

    // Limpiar radios
    document.querySelectorAll('input[name="usuario_seleccionado_asignacion"]').forEach(radio => {
        radio.checked = false;
    });

    // Mostrar todos los usuarios
    filtrarUsuariosAsignacion('');

    // Cargar estadísticas de usuarios
    cargarEstadisticasUsuarios();

    // Mostrar modal
    const modal = new bootstrap.Modal(document.getElementById('modalAsignarTicket'));
    modal.show();
}

function enviarAsignacion() {
    if (!usuarioSeleccionadoIdAsignacion) {
        mostrarAlerta('error', 'Por favor seleccione un usuario');
        return;
    }

    const formData = new FormData();
    formData.append('ticket_id', ticketIdActual);
    formData.append('usuario_id', usuarioSeleccionadoIdAsignacion);
    formData.append('nota', document.getElementById('nota_asignacion').value);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    const btnAsignar = document.getElementById('btn-asignar');
    btnAsignar.disabled = true;
    btnAsignar.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Asignando...';

    fetch('{% url "asignaciones:crear_asignacion" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Cerrar modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalAsignarTicket'));
            modal.hide();

            // Mostrar mensaje de éxito
            mostrarAlerta('success', data.message);

            // Recargar la lista
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            mostrarAlerta('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        mostrarAlerta('error', 'Error de conexión al asignar el ticket');
    })
    .finally(() => {
        btnAsignar.disabled = false;
        btnAsignar.innerHTML = '<i class="fas fa-user-plus me-2"></i>Asignar Ticket';
    });
}

// Funciones para el modal mejorado
function filtrarUsuariosAsignacion(termino) {
    const items = document.querySelectorAll('.usuario-item-asignacion');
    const contador = document.getElementById('contador-usuarios-asignacion');
    let visibles = 0;

    items.forEach(item => {
        const nombre = item.dataset.nombre || '';
        const email = item.dataset.email || '';
        const areas = item.dataset.areas || '';

        const coincide = nombre.includes(termino.toLowerCase()) ||
                       email.includes(termino.toLowerCase()) ||
                       areas.includes(termino.toLowerCase());

        if (coincide) {
            item.style.display = 'block';
            visibles++;
        } else {
            item.style.display = 'none';
        }
    });

    contador.textContent = visibles;
}

function seleccionarUsuarioAsignacion(usuarioId, usuarioNombre) {
    usuarioSeleccionadoIdAsignacion = usuarioId;

    // Actualizar radio
    const radio = document.getElementById(`usuario_asignacion_${usuarioId}`);
    if (radio) radio.checked = true;

    // Mostrar información del usuario seleccionado
    document.getElementById('info-usuario-seleccionado-asignacion').textContent = usuarioNombre;
    document.getElementById('usuario-seleccionado-asignacion').style.display = 'block';

    // Habilitar botón
    document.getElementById('btn-asignar').disabled = false;

    // Scroll al usuario seleccionado
    document.getElementById('usuario-seleccionado-asignacion').scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
    });
}

function removerSeleccionAsignacion() {
    usuarioSeleccionadoIdAsignacion = null;
    document.getElementById('usuario-seleccionado-asignacion').style.display = 'none';
    document.getElementById('btn-asignar').disabled = true;

    // Limpiar radios
    document.querySelectorAll('input[name="usuario_seleccionado_asignacion"]').forEach(radio => {
        radio.checked = false;
    });
}

function cargarEstadisticasUsuarios() {
    // Cargar estadísticas de asignaciones por usuario
    document.querySelectorAll('.asignaciones-count-asignacion').forEach(span => {
        const usuarioId = span.dataset.usuario;

        fetch(`/asignaciones/ajax/estadisticas-usuario/?usuario_id=${usuarioId}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                span.textContent = data.asignaciones_activas || 0;
            }
        })
        .catch(error => {
            console.error('Error cargando estadísticas:', error);
            span.textContent = '0';
        });
    });
}

function mostrarAlerta(tipo, mensaje) {
    const config = {
        text: mensaje,
        timer: 3000,
        showConfirmButton: false,
        toast: true,
        position: 'top-end'
    };

    if (tipo === 'success') {
        config.icon = 'success';
        config.title = '¡Éxito!';
    } else {
        config.icon = 'error';
        config.title = 'Error';
    }

    Swal.fire(config);
}
</script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
{% endblock %}
