<div class="table-responsive">
    <table class="table table-hover align-middle small">
        <thead class="table-dark">
            <tr>
                <th><i class="fas fa-id-card me-1"></i>DPI</th>
                <th><i class="fas fa-user me-1"></i>Usuario</th>
                <th><i class="fas fa-user-circle me-1"></i>Nombre Completo</th>
                <th><i class="fas fa-briefcase me-1"></i>Cargo</th>
                <th><i class="fas fa-building me-1"></i>Área</th>
                <th><i class="fas fa-phone me-1"></i>Teléfonos</th>
                <th><i class="fas fa-users me-1"></i>Familiares</th>
                <th><i class="fas fa-toggle-on me-1"></i>Estado</th>
                <th width="200"><i class="fas fa-cogs me-1"></i>Acciones</th>
            </tr>
        </thead>
        <tbody>
        {% for usuario in usuarios %}
            <tr>
                <td>
                    <code class="text-primary">{{ usuario.dpi }}</code>
                </td>
                <td>
                    <strong>{{ usuario.username }}</strong>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-circle me-2">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <div class="fw-bold">{{ usuario.get_full_name }}</div>
                            <small class="text-muted">{{ usuario.email|default:"Sin email" }}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-primary">{{ usuario.cargo.nombre|default:"Sin cargo" }}</span>
                </td>
                <td>
                    {% for grupo in usuario.groups.all %}
                        <span class="badge bg-info me-1">
                            <i class="fas fa-users me-1"></i>{{ grupo.name }}
                        </span>
                    {% empty %}
                        <span class="text-muted small">Sin área</span>
                    {% endfor %}
                </td>
                <td>
                    {% with telefonos=usuario.celulares.all %}
                        {% if telefonos %}
                            {% for cel in telefonos|slice:":2" %}
                                <div class="telefono-item mb-1">
                                    <span class="badge bg-info fs-6">
                                        <i class="fas fa-phone me-1"></i>{{ cel.numero }}
                                    </span>
                                </div>
                            {% endfor %}
                            {% if telefonos.count > 2 %}
                                <div class="telefono-item">
                                    <span class="badge bg-secondary fs-6">
                                        <i class="fas fa-plus me-1"></i>{{ telefonos.count|add:"-2" }} más
                                    </span>
                                </div>
                            {% endif %}
                        {% else %}
                            <span class="text-muted">
                                <i class="fas fa-phone-slash me-1"></i>Sin teléfono
                            </span>
                        {% endif %}
                    {% endwith %}
                </td>
                <td>
                    {% with familiares_count=usuario.familiares.count %}
                        {% if familiares_count > 0 %}
                            <span class="badge bg-success">
                                <i class="fas fa-users me-1"></i>{{ familiares_count }}
                            </span>
                        {% else %}
                            <span class="text-muted">
                                <i class="fas fa-user-slash me-1"></i>Sin familiares
                            </span>
                        {% endif %}
                    {% endwith %}
                </td>
                <td>
                    {% if usuario.is_active %}
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle me-1"></i>Activo
                        </span>
                    {% else %}
                        <span class="badge bg-danger">
                            <i class="fas fa-times-circle me-1"></i>Inactivo
                        </span>
                    {% endif %}
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <!-- Ver detalles -->
                        <a href="{% url 'user:detalle_usuario' usuario.pk %}"
                           class="btn btn-sm btn-outline-info"
                           title="Ver detalles">
                            <i class="fas fa-eye"></i>
                        </a>

                        {% if perms.user.change_user %}
                        <!-- Editar -->
                        <a href="{% url 'user:editar_usuario' usuario.pk %}"
                           class="btn btn-sm btn-outline-primary"
                           title="Editar usuario">
                            <i class="fas fa-edit"></i>
                        </a>

                        <!-- Gestionar teléfonos -->
                        <a href="{% url 'user:gestionar_telefonos' usuario.pk %}"
                           class="btn btn-sm btn-outline-secondary"
                           title="Gestionar teléfonos">
                            <i class="fas fa-phone"></i>
                        </a>

                        <!-- Gestionar familiares -->
                        <a href="{% url 'user:gestionar_familiares' usuario.pk %}"
                           class="btn btn-sm btn-outline-success"
                           title="Gestionar familiares">
                            <i class="fas fa-users"></i>
                        </a>
                        {% endif %}

                        {% if perms.user.delete_user %}
                        <!-- Cambiar estado -->
                        {% if usuario.is_active %}
                            <button class="btn btn-sm btn-outline-warning"
                                    onclick="cambiarEstadoUsuario({{ usuario.pk }}, false)"
                                    title="Desactivar usuario">
                                <i class="fas fa-user-slash"></i>
                            </button>
                        {% else %}
                            <button class="btn btn-sm btn-outline-success"
                                    onclick="cambiarEstadoUsuario({{ usuario.pk }}, true)"
                                    title="Activar usuario">
                                <i class="fas fa-user-check"></i>
                            </button>
                        {% endif %}
                        {% endif %}
                    </div>
                </td>
            </tr>
        {% empty %}
            <tr>
                <td colspan="9" class="text-center py-4">
                    <div class="text-muted">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <h5>No hay usuarios registrados</h5>
                        <p>Comienza creando tu primer usuario</p>
                        {% if perms.user.add_user %}
                        <a href="{% url 'user:crear_usuario_paso1' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Crear Usuario
                        </a>
                        {% endif %}
                    </div>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
</div>

<style>
.avatar-circle {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(45deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function cambiarEstadoUsuario(pk, activar) {
    const accion = activar ? 'activar' : 'desactivar';
    const titulo = activar ? '¿Activar usuario?' : '¿Desactivar usuario?';
    const texto = activar ?
        'El usuario podrá acceder al sistema nuevamente.' :
        'El usuario no podrá acceder al sistema.';
    const confirmText = activar ? 'Sí, activar' : 'Sí, desactivar';
    const successTitle = activar ? 'Activado' : 'Desactivado';
    const successText = activar ?
        'El usuario ha sido activado correctamente.' :
        'El usuario ha sido desactivado correctamente.';

    Swal.fire({
        title: titulo,
        text: texto,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: activar ? '#28a745' : '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: confirmText,
        cancelButtonText: 'Cancelar'
    }).then((result) => {
        if (result.isConfirmed) {
            // Mostrar loading
            Swal.fire({
                title: 'Procesando...',
                text: `${accion === 'activar' ? 'Activando' : 'Desactivando'} usuario`,
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Usar las URLs correctas según la acción
            const url = activar ?
                "{% url 'user:activar_usuario' 0 %}".replace('0', pk) :
                "{% url 'user:desactivar_usuario_definitivo' 0 %}".replace('0', pk);

            fetch(url, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: successTitle,
                        text: successText,
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message || 'Ocurrió un error al cambiar el estado del usuario.'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error de conexión',
                    text: 'No se pudo conectar con el servidor.'
                });
            });
        }
    });
}

// Tooltips para los botones
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips de Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>