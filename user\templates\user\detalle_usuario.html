{% extends 'Base/base.html' %}
{% block title %}Detalle de Usuario{% endblock %}

{% block content %}
<div class="card mb-3">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h4 class="mb-0">{{ usuario.get_full_name }}</h4>
        <div>
            <a href="{% url 'user:lista_usuarios' %}" class="btn btn-outline-secondary btn-sm me-2">
                <i class="fas fa-arrow-left me-1"></i>Volver
            </a>
            {% if puede_editar %}
            <a href="{% url 'user:editar_usuario' usuario.pk %}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-edit me-1"></i>Editar
            </a>
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        <dl class="row">
            <dt class="col-sm-3">DPI</dt>
            <dd class="col-sm-9">{{ usuario.dpi }}</dd>
            <dt class="col-sm-3">Usuario</dt>
            <dd class="col-sm-9">{{ usuario.username }}</dd>
            <dt class="col-sm-3">Cargo</dt>
            <dd class="col-sm-9">{{ usuario.cargo.nombre|default:"Sin cargo asignado" }}</dd>
            <dt class="col-sm-3">Área/Departamento</dt>
            <dd class="col-sm-9">
                {% for grupo in usuario.groups.all %}
                    <span class="badge bg-primary me-1">
                        <i class="fas fa-users me-1"></i>{{ grupo.name }}
                    </span>
                {% empty %}
                    <span class="text-muted">Sin área asignada</span>
                {% endfor %}
            </dd>
            <dt class="col-sm-3">Estado</dt>
            <dd class="col-sm-9">
                {% if usuario.is_active %}
                    <span class="badge bg-success">
                        <i class="fas fa-user-check me-1"></i>Usuario Activo
                    </span>
                    <small class="text-muted ms-2">Puede acceder al sistema</small>
                {% else %}
                    <span class="badge bg-danger">
                        <i class="fas fa-user-slash me-1"></i>Usuario Inactivo
                    </span>
                    <small class="text-muted ms-2">No puede acceder al sistema</small>
                {% endif %}
            </dd>
            <dt class="col-sm-3">Fecha de nacimiento</dt>
            <dd class="col-sm-9">{{ usuario.fecha_nacimiento }}</dd>
            <dt class="col-sm-3">Género</dt>
            <dd class="col-sm-9">{{ usuario.get_genero_display }}</dd>
            <dt class="col-sm-3">Supervisor</dt>
            <dd class="col-sm-9">{{ usuario.is_supervisor|yesno:"Sí,No" }}</dd>
        </dl>
        <hr>
        <h5>Teléfonos</h5>
        <ul>
            {% for cel in usuario.celulares.all %}
                <li>{{ cel.numero }} ({{ cel.get_tipo_display }})</li>
            {% empty %}
                <li class="text-muted">Sin teléfonos registrados</li>
            {% endfor %}
        </ul>
        <hr>
        <h5>Familiares</h5>
        <ul>
            {% for fam in usuario.familiares.all %}
                <li>
                    {{ fam.nombre }} ({{ fam.parentesco }})
                    <ul>
                        {% for cel in fam.celulares_emergencia.all %}
                            <li>{{ cel.numero }}</li>
                        {% empty %}
                            <li class="text-muted">Sin teléfonos de emergencia</li>
                        {% endfor %}
                    </ul>
                </li>
            {% empty %}
                <li class="text-muted">Sin familiares registrados</li>
            {% endfor %}
        </ul>
    </div>
</div>
{% endblock %}