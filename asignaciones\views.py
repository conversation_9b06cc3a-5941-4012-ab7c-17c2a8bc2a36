"""
asignaciones/views.py
Vistas para la gestión centralizada de asignaciones de tickets.

Implementa funcionalidades para:
- Lista de asignaciones con filtros por área y estado
- Gestión de asignaciones por supervisores y administradores
- Reportes de productividad y estadísticas
- AJAX para gestión dinámica

Reglas de permisos:
- Administradores: Ven todas las asignaciones, pueden asignar a cualquiera
- Supervisores: Solo ven asignaciones de su área, asignan a su equipo
- Empleados: Solo ven sus propias asignaciones
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponseForbidden, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count, Case, When, Inte<PERSON><PERSON><PERSON>, Avg, Sum
from django.template.loader import render_to_string
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from django.contrib.auth.models import Group
from django.contrib.auth import get_user_model

# Usar el sistema centralizado de permisos
from permissions.decorators import can_assign_tickets_required, supervisor_or_admin_required

User = get_user_model()
from datetime import datetime, timedelta

from .models import AsignacionTicket
from tickets.models import Ticket, HistorialTicket
from notificaciones.models import Notificacion
from .utils import (
    crear_notificacion_asignacion_usuario,
    crear_notificacion_trabajo_iniciado,
    crear_notificacion_trabajo_finalizado
)

# ============================================================================
# FUNCIONES AUXILIARES PARA PERMISOS Y FILTROS
# ============================================================================

def _es_administrador(user):
    """Verifica si el usuario es administrador o superusuario."""
    return (user.groups.filter(name='Admin').exists() or
            (user.cargo and user.cargo.nombre == 'Administrador') or
            user.is_superuser)


def _es_supervisor(user):
    """Verifica si el usuario es supervisor."""
    return (user.groups.filter(name='Supervisor').exists() or
            (user.cargo and user.cargo.nombre == 'Supervisor'))


def _puede_gestionar_asignaciones(user):
    """Verifica si el usuario puede gestionar asignaciones."""
    return _es_administrador(user) or _es_supervisor(user)


def _get_asignaciones_queryset_by_user(user):
    """
    Obtiene el queryset de asignaciones según el rol del usuario.
    Optimizado con select_related y prefetch_related para evitar N+1 queries.

    Args:
        user: Usuario autenticado

    Returns:
        QuerySet: Asignaciones que el usuario puede ver
    """
    base_queryset = AsignacionTicket.objects.select_related(
        'ticket',
        'ticket__grupo',
        'ticket__creado_por',
        'usuario',
        'usuario__cargo',
        'asignado_por'
    ).prefetch_related(
        'ticket__imagenes',
        'ticket__ciudadanos__ciudadano'
    ).filter(is_active=True)

    if _es_administrador(user):
        # Administradores ven todas las asignaciones
        return base_queryset
    elif _es_supervisor(user):
        # Supervisores solo ven asignaciones de su área
        grupos_usuario = user.groups.all()
        return base_queryset.filter(ticket__grupo__in=grupos_usuario)
    else:
        # Empleados solo ven sus propias asignaciones
        return base_queryset.filter(usuario=user)


def _get_tickets_sin_asignar_queryset_by_user(user):
    """
    Obtiene tickets sin asignar según el rol del usuario.

    Args:
        user: Usuario autenticado

    Returns:
        QuerySet: Tickets sin asignar que el usuario puede gestionar
    """
    # Solo supervisores y administradores pueden ver tickets sin asignar
    if not _puede_gestionar_asignaciones(user):
        return Ticket.objects.none()

    base_queryset = Ticket.objects.select_related(
        'grupo',
        'creado_por',
        'creado_por__cargo'
    ).prefetch_related(
        'imagenes',
        'ciudadanos__ciudadano'
    ).filter(
        is_active=True,
        asignaciones__isnull=True  # Sin asignaciones
    ).annotate(
        total_asignaciones=Count('asignaciones', filter=Q(asignaciones__is_active=True))
    ).filter(total_asignaciones=0)

    if _es_administrador(user):
        # Administradores ven todos los tickets sin asignar
        return base_queryset
    else:
        # Supervisores solo ven tickets sin asignar de su área
        grupos_usuario = user.groups.all()
        return base_queryset.filter(grupo__in=grupos_usuario)


# ============================================================================
# VISTAS PRINCIPALES DE ASIGNACIONES
# ============================================================================

@supervisor_or_admin_required
def lista_asignaciones(request):
    """
    Lista centralizada de asignaciones con filtros avanzados.

    Muestra diferentes vistas según el rol:
    - Administradores: Todas las asignaciones con filtro por área
    - Supervisores: Solo asignaciones de su área
    - Empleados: Solo sus propias asignaciones

    Args:
        request: HttpRequest object con filtros opcionales

    Returns:
        HttpResponse: Render del template con lista de asignaciones
        JsonResponse: Para peticiones AJAX (scroll infinito)

    Security:
        - Requiere autenticación (@login_required)
        - Filtros por rol automáticos
    """
    user = request.user

    # Query base según permisos del usuario
    asignaciones_queryset = _get_asignaciones_queryset_by_user(user)
    tickets_sin_asignar_queryset = _get_tickets_sin_asignar_queryset_by_user(user)

    # Aplicar filtros
    area_id = request.GET.get('area')
    estado = request.GET.get('estado')
    usuario_id = request.GET.get('usuario')
    busqueda = request.GET.get('q', '').strip()
    mostrar_sin_asignar = request.GET.get('sin_asignar', 'true') == 'true'

    # Filtro por área (solo para administradores)
    if area_id and _es_administrador(user):
        asignaciones_queryset = asignaciones_queryset.filter(ticket__grupo_id=area_id)
        tickets_sin_asignar_queryset = tickets_sin_asignar_queryset.filter(grupo_id=area_id)

    # Filtro por estado
    if estado:
        asignaciones_queryset = asignaciones_queryset.filter(estado=estado)

    # Filtro por usuario asignado
    if usuario_id:
        asignaciones_queryset = asignaciones_queryset.filter(usuario_id=usuario_id)

    # Búsqueda por título de ticket o nombre de usuario
    if busqueda:
        asignaciones_queryset = asignaciones_queryset.filter(
            Q(ticket__titulo__icontains=busqueda) |
            Q(ticket__descripcion__icontains=busqueda) |
            Q(usuario__first_name__icontains=busqueda) |
            Q(usuario__last_name__icontains=busqueda) |
            Q(usuario__username__icontains=busqueda)
        )
        tickets_sin_asignar_queryset = tickets_sin_asignar_queryset.filter(
            Q(titulo__icontains=busqueda) |
            Q(descripcion__icontains=busqueda)
        )

    # Enfoque optimizado: usar paginación directa en lugar de cargar todo en memoria
    page = request.GET.get('page', 1)
    items_per_page = 15  # Reducir para mejor rendimiento

    # Calcular offset para paginación manual
    try:
        page_num = int(page)
    except (ValueError, TypeError):
        page_num = 1

    offset = (page_num - 1) * items_per_page

    # Obtener tickets sin asignar paginados (solo en primera página)
    tickets_sin_asignar = []
    if page_num == 1 and mostrar_sin_asignar and _puede_gestionar_asignaciones(user):
        tickets_sin_asignar = list(
            tickets_sin_asignar_queryset.order_by('-fecha_creacion')[:5]  # Máximo 5 tickets sin asignar
        )

    # Obtener asignaciones paginadas directamente de la BD
    # Usar distinct para evitar duplicados por ticket
    asignaciones_paginadas = asignaciones_queryset.order_by(
        '-fecha_asignacion'
    )[offset:offset + items_per_page]

    # Agrupar asignaciones por ticket de forma eficiente
    tickets_con_asignaciones = {}
    for asignacion in asignaciones_paginadas:
        ticket_id = asignacion.ticket.id
        if ticket_id not in tickets_con_asignaciones:
            tickets_con_asignaciones[ticket_id] = {
                'ticket': asignacion.ticket,
                'asignaciones': [asignacion],
                'ultima_asignacion': asignacion
            }
        else:
            tickets_con_asignaciones[ticket_id]['asignaciones'].append(asignacion)

    # Crear lista final optimizada
    items_list = []

    # Agregar tickets sin asignar (solo primera página)
    for ticket in tickets_sin_asignar:
        items_list.append({
            'tipo': 'ticket_sin_asignar',
            'ticket': ticket,
            'fecha_orden': ticket.fecha_creacion
        })

    # Agregar tickets con asignaciones
    for ticket_data in tickets_con_asignaciones.values():
        items_list.append({
            'tipo': 'ticket_con_asignaciones',
            'ticket': ticket_data['ticket'],
            'asignaciones': ticket_data['asignaciones'],
            'ultima_asignacion': ticket_data['ultima_asignacion'],
            'total_asignaciones': len(ticket_data['asignaciones'])
        })

    # Simular paginación para compatibilidad con templates
    class MockPaginator:
        def __init__(self, items, has_next):
            self.object_list = items
            self._has_next = has_next

        def has_next(self):
            return self._has_next

        def __iter__(self):
            return iter(self.object_list)

    # Determinar si hay más páginas
    total_asignaciones = asignaciones_queryset.count()
    has_next = (offset + items_per_page) < total_asignaciones

    items = MockPaginator(items_list, has_next)

    # Respuesta AJAX para scroll infinito
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        html = render_to_string(
            'asignaciones/parciales/lista_asignaciones.html',
            {'items': items, 'user': user},
            request=request
        )
        return JsonResponse({
            'html': html,
            'has_next': has_next
        })

    # Obtener datos para filtros
    areas_disponibles = []
    usuarios_disponibles = []

    if _es_administrador(user):
        areas_disponibles = Group.objects.all().order_by('name')
        usuarios_disponibles = User.objects.filter(is_active=True).order_by('first_name', 'last_name')
    elif _es_supervisor(user):
        grupos_usuario = user.groups.all()
        usuarios_disponibles = User.objects.filter(
            groups__in=grupos_usuario,
            is_active=True
        ).distinct().order_by('first_name', 'last_name')

    # Estadísticas optimizadas con una sola consulta
    stats_query = asignaciones_queryset.aggregate(
        total_asignaciones=Count('id'),
        asignaciones_activas=Count(
            Case(When(estado__in=[1, 2], then=1), output_field=IntegerField())
        ),
        asignaciones_finalizadas=Count(
            Case(When(estado=3, then=1), output_field=IntegerField())
        )
    )

    stats = {
        'total_asignaciones': stats_query['total_asignaciones'],
        'tickets_sin_asignar': tickets_sin_asignar_queryset.count(),  # Esta es rápida por los índices
        'asignaciones_activas': stats_query['asignaciones_activas'],
        'asignaciones_finalizadas': stats_query['asignaciones_finalizadas']
    }

    # Contexto para template principal
    context = {
        'items': items,
        'stats': stats,
        'areas_disponibles': areas_disponibles,
        'usuarios_disponibles': usuarios_disponibles,
        'filtros': {
            'area_id': area_id,
            'estado': estado,
            'usuario_id': usuario_id,
            'busqueda': busqueda,
            'mostrar_sin_asignar': mostrar_sin_asignar
        },
        'es_administrador': _es_administrador(user),
        'es_supervisor': _es_supervisor(user),
        'puede_gestionar': _puede_gestionar_asignaciones(user)
    }

    return render(request, 'asignaciones/lista_asignaciones.html', context)

@login_required
def mis_asignaciones(request):
    """
    Vista personalizada para que cada usuario vea sus propias asignaciones.

    Muestra estadísticas personales y lista de asignaciones del usuario.

    Args:
        request: HttpRequest object

    Returns:
        HttpResponse: Render del template con asignaciones del usuario

    Security:
        - Requiere autenticación (@login_required)
        - Solo muestra asignaciones del usuario autenticado
    """
    user = request.user

    # Obtener asignaciones del usuario
    asignaciones = AsignacionTicket.objects.filter(
        usuario=user,
        is_active=True
    ).select_related(
        'ticket', 'ticket__grupo', 'ticket__creado_por', 'asignado_por'
    ).order_by('-fecha_asignacion')

    # Aplicar filtros
    estado = request.GET.get('estado')
    if estado:
        asignaciones = asignaciones.filter(estado=estado)

    # Paginación
    paginator = Paginator(asignaciones, 15)
    page = request.GET.get('page', 1)
    asignaciones_paginadas = paginator.get_page(page)

    # Estadísticas personales
    stats = {
        'total_asignaciones': AsignacionTicket.objects.filter(usuario=user, is_active=True).count(),
        'asignadas': AsignacionTicket.objects.filter(usuario=user, is_active=True, estado=1).count(),
        'en_progreso': AsignacionTicket.objects.filter(usuario=user, is_active=True, estado=2).count(),
        'finalizadas': AsignacionTicket.objects.filter(usuario=user, is_active=True, estado=3).count(),
        'tiempo_promedio': AsignacionTicket.objects.filter(
            usuario=user,
            is_active=True,
            estado=3,
            fecha_finalizacion__isnull=False
        ).aggregate(
            promedio=Avg('fecha_finalizacion') - Avg('fecha_asignacion')
        )['promedio']
    }

    # Contexto
    context = {
        'asignaciones': asignaciones_paginadas,
        'stats': stats,
        'estado_filtro': estado,
        'usuario': user
    }

    return render(request, 'asignaciones/mis_asignaciones.html', context)

def _puede_asignar_ticket(user, ticket):
    """
    Verifica si un usuario puede asignar un ticket.

    Args:
        user: Usuario autenticado
        ticket: Instancia del ticket

    Returns:
        bool: True si puede asignar, False en caso contrario
    """
    if _es_administrador(user):
        return True
    elif _es_supervisor(user):
        # Supervisor puede asignar solo en su área
        return user.groups.filter(id=ticket.grupo.id).exists()
    else:
        return False


def _get_usuarios_disponibles_asignacion(user, ticket):
    """
    Obtiene los usuarios disponibles para asignar al ticket.

    Args:
        user: Usuario autenticado
        ticket: Instancia del ticket

    Returns:
        QuerySet: Usuarios disponibles para asignación
    """
    if not _puede_asignar_ticket(user, ticket):
        return User.objects.none()

    if _es_administrador(user):
        # Administrador puede asignar a cualquier usuario activo
        return User.objects.filter(is_active=True).order_by('first_name', 'last_name')
    else:
        # Supervisor puede asignar solo a usuarios de su área
        return User.objects.filter(
            groups=ticket.grupo,
            is_active=True
        ).order_by('first_name', 'last_name')


def _crear_notificacion_asignacion(ticket, asignado_por, usuario_asignado):
    """
    Crea notificación para asignación de ticket.

    Args:
        ticket: Instancia del ticket
        asignado_por: Usuario que realiza la asignación
        usuario_asignado: Usuario al que se asigna el ticket
    """
    try:
        # Crear notificación para el usuario asignado
        notificacion = Notificacion.objects.create(
            mensaje=f'Te han asignado el ticket #{ticket.id}: {ticket.titulo}',
            tipo='ticket',
            ticket=ticket,
            titulo='Nuevo Ticket Asignado',
            creado_por=asignado_por
        )

        # Enviar al usuario asignado
        notificacion.enviar_a_usuario(usuario_asignado)

        # También notificar al creador del ticket si es diferente
        if ticket.creado_por != asignado_por and ticket.creado_por != usuario_asignado:
            notificacion_creador = Notificacion.objects.create(
                mensaje=f'Ticket #{ticket.id} ha sido asignado a {usuario_asignado.get_full_name() or usuario_asignado.username}',
                tipo='ticket',
                ticket=ticket,
                titulo='Ticket Asignado',
                creado_por=asignado_por
            )
            notificacion_creador.enviar_a_usuario(ticket.creado_por)

    except Exception as e:
        print(f"Error al crear notificación de asignación: {e}")


def _crear_notificacion_desasignacion(ticket, desasignado_por, usuario_desasignado, motivo):
    """
    Crea notificación para desasignación de ticket.

    Args:
        ticket: Instancia del ticket
        desasignado_por: Usuario que realiza la desasignación
        usuario_desasignado: Usuario del que se desasigna el ticket
        motivo: Motivo de la desasignación
    """
    try:
        mensaje = f'Has sido desasignado del ticket #{ticket.id}: {ticket.titulo}'
        if motivo:
            mensaje += f'\nMotivo: {motivo}'

        notificacion = Notificacion.objects.create(
            mensaje=mensaje,
            tipo='warning',
            ticket=ticket,
            titulo='Ticket Desasignado',
            creado_por=desasignado_por
        )

        # Enviar al usuario desasignado
        notificacion.enviar_a_usuario(usuario_desasignado)

    except Exception as e:
        print(f"Error al crear notificación de desasignación: {e}")


@login_required
def asignaciones_grupo(request):
    """
    Vista de asignaciones por grupo (para supervisores).
    Redirige a la lista principal con filtros aplicados.
    """
    user = request.user

    if not _es_supervisor(user):
        messages.error(request, 'No tienes permisos para ver asignaciones de grupo.')
        return redirect('asignaciones:mis_asignaciones')

    # Redirigir a lista principal (ya filtrada por área del supervisor)
    return redirect('asignaciones:lista_asignaciones')

@can_assign_tickets_required
@csrf_protect
@require_http_methods(["POST"])
def crear_asignacion(request):
    """
    Asigna un ticket a un usuario específico.

    Reglas de asignación:
    - Solo supervisores y administradores pueden asignar
    - Supervisores solo pueden asignar en su área
    - Administradores pueden asignar a cualquier usuario

    Args:
        request: HttpRequest object con datos del formulario

    Returns:
        JsonResponse: Resultado de la operación para AJAX
        HttpResponse: Redirección para peticiones normales

    Security:
        - Requiere autenticación (@login_required)
        - Protección CSRF (@csrf_protect)
        - Solo métodos POST (@require_http_methods)
        - Validación de permisos por rol
    """
    user = request.user

    # Obtener datos del formulario
    ticket_id = request.POST.get('ticket_id')
    usuario_id = request.POST.get('usuario_id')
    nota = request.POST.get('nota', '').strip()

    if not ticket_id or not usuario_id:
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'Datos incompletos.'
            })
        messages.error(request, 'Datos incompletos.')
        return redirect('asignaciones:lista_asignaciones')

    # Obtener ticket
    ticket = get_object_or_404(
        Ticket.objects.select_related('creado_por', 'grupo'),
        id=ticket_id,
        is_active=True
    )

    # Verificar permisos
    if not _puede_asignar_ticket(user, ticket):
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'No tienes permisos para asignar este ticket.'
            })
        messages.error(request, 'No tienes permisos para asignar este ticket.')
        return redirect('asignaciones:lista_asignaciones')

    # Obtener usuario a asignar
    try:
        usuario_asignado = User.objects.get(id=usuario_id, is_active=True)
    except User.DoesNotExist:
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'Usuario no encontrado.'
            })
        messages.error(request, 'Usuario no encontrado.')
        return redirect('asignaciones:lista_asignaciones')

    # Verificar que el usuario puede ser asignado a este ticket
    usuarios_disponibles = _get_usuarios_disponibles_asignacion(user, ticket)
    if not usuarios_disponibles.filter(id=usuario_id).exists():
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'No puedes asignar el ticket a este usuario.'
            })
        messages.error(request, 'No puedes asignar el ticket a este usuario.')
        return redirect('asignaciones:lista_asignaciones')

    # Verificar si ya existe una asignación activa para este usuario
    asignacion_existente = AsignacionTicket.objects.filter(
        ticket=ticket,
        usuario=usuario_asignado,
        is_active=True
    ).first()

    if asignacion_existente:
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': f'El ticket ya está asignado a {usuario_asignado.get_full_name() or usuario_asignado.username}.'
            })
        messages.warning(request, f'El ticket ya está asignado a {usuario_asignado.get_full_name() or usuario_asignado.username}.')
        return redirect('asignaciones:lista_asignaciones')

    # Crear la asignación
    asignacion = AsignacionTicket.objects.create(
        ticket=ticket,
        usuario=usuario_asignado,
        asignado_por=user,
        nota=nota
    )

    # Cambiar estado del ticket a "Asignado" si está en "Abierto"
    if ticket.estado == 1:  # Abierto
        ticket.estado = 1  # Mantener como abierto pero ahora asignado
        ticket.save()

    # Registrar en historial
    HistorialTicket.registrar_cambio(
        ticket=ticket,
        usuario=user,
        accion=f'Ticket asignado a {usuario_asignado.get_full_name() or usuario_asignado.username}',
        detalles={
            'usuario_asignado': usuario_asignado.username,
            'usuario_asignado_nombre': usuario_asignado.get_full_name() or usuario_asignado.username,
            'asignado_por': user.username,
            'nota': nota if nota else None
        }
    )

    # Crear notificación automática para el usuario asignado
    crear_notificacion_asignacion_usuario(
        ticket=ticket,
        usuario_asignado=usuario_asignado,
        usuario_asignador=user,
        nota=nota
    )

    # Crear notificación para el usuario asignado
    _crear_notificacion_asignacion(ticket, user, usuario_asignado)

    # Respuesta
    mensaje = f'Ticket #{ticket.id} asignado exitosamente a {usuario_asignado.get_full_name() or usuario_asignado.username}'

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': mensaje,
            'usuario_asignado': {
                'id': usuario_asignado.id,
                'nombre': usuario_asignado.get_full_name() or usuario_asignado.username,
                'username': usuario_asignado.username
            }
        })

    messages.success(request, mensaje)
    return redirect('asignaciones:lista_asignaciones')

def editar_asignacion(request, asignacion_id):
    return HttpResponse(f"Editar asignación {asignacion_id} - En desarrollo")


@login_required
@csrf_protect
@require_http_methods(["POST"])
def iniciar_asignacion(request, asignacion_id):
    """
    Inicia el trabajo en una asignación específica.

    Solo el usuario asignado puede iniciar su propio trabajo.

    Args:
        request: HttpRequest object
        asignacion_id: ID de la asignación a iniciar

    Returns:
        JsonResponse: Resultado de la operación

    Security:
        - Requiere autenticación (@login_required)
        - Protección CSRF (@csrf_protect)
        - Solo métodos POST (@require_http_methods)
        - Solo el usuario asignado puede iniciar
    """
    user = request.user

    # Obtener asignación
    asignacion = get_object_or_404(
        AsignacionTicket.objects.select_related('ticket', 'usuario'),
        id=asignacion_id,
        is_active=True
    )

    # Verificar que el usuario puede iniciar esta asignación
    if asignacion.usuario != user:
        return JsonResponse({
            'success': False,
            'message': 'Solo puedes iniciar tus propias asignaciones.'
        })

    # Verificar estado actual
    if asignacion.estado != 1:  # Debe estar en estado "Asignado"
        return JsonResponse({
            'success': False,
            'message': f'La asignación está en estado "{asignacion.get_estado_display()}" y no se puede iniciar.'
        })

    # Iniciar trabajo
    try:
        asignacion.iniciar_trabajo()

        # Registrar en historial
        HistorialTicket.registrar_cambio(
            ticket=asignacion.ticket,
            usuario=user,
            accion=f'Trabajo iniciado por {user.get_full_name() or user.username}',
            detalles={
                'asignacion_id': asignacion.id,
                'usuario': user.username,
                'fecha_inicio': asignacion.fecha_inicio.isoformat() if asignacion.fecha_inicio else None
            }
        )

        # Crear notificación automática
        crear_notificacion_trabajo_iniciado(asignacion)

        return JsonResponse({
            'success': True,
            'message': 'Trabajo iniciado exitosamente.',
            'nuevo_estado': asignacion.get_estado_display()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error al iniciar el trabajo: {str(e)}'
        })

@login_required
@csrf_protect
@require_http_methods(["POST"])
def finalizar_asignacion(request, asignacion_id):
    """
    Finaliza el trabajo en una asignación específica.

    Solo el usuario asignado puede finalizar su propio trabajo.

    Args:
        request: HttpRequest object con nota opcional
        asignacion_id: ID de la asignación a finalizar

    Returns:
        JsonResponse: Resultado de la operación

    Security:
        - Requiere autenticación (@login_required)
        - Protección CSRF (@csrf_protect)
        - Solo métodos POST (@require_http_methods)
        - Solo el usuario asignado puede finalizar
    """
    user = request.user

    # Obtener asignación
    asignacion = get_object_or_404(
        AsignacionTicket.objects.select_related('ticket', 'usuario'),
        id=asignacion_id,
        is_active=True
    )

    # Verificar que el usuario puede finalizar esta asignación
    if asignacion.usuario != user:
        return JsonResponse({
            'success': False,
            'message': 'Solo puedes finalizar tus propias asignaciones.'
        })

    # Verificar estado actual
    if asignacion.estado != 2:  # Debe estar en estado "En Progreso"
        return JsonResponse({
            'success': False,
            'message': f'La asignación está en estado "{asignacion.get_estado_display()}" y no se puede finalizar.'
        })

    # Obtener nota de finalización
    nota = request.POST.get('nota', '').strip()

    # Finalizar trabajo
    try:
        asignacion.finalizar_trabajo(nota=nota)

        # Registrar en historial
        HistorialTicket.registrar_cambio(
            ticket=asignacion.ticket,
            usuario=user,
            accion=f'Trabajo finalizado por {user.get_full_name() or user.username}',
            detalles={
                'asignacion_id': asignacion.id,
                'usuario': user.username,
                'fecha_finalizacion': asignacion.fecha_finalizacion.isoformat() if asignacion.fecha_finalizacion else None,
                'nota': nota if nota else None
            }
        )

        # Crear notificación automática
        crear_notificacion_trabajo_finalizado(asignacion, nota)

        return JsonResponse({
            'success': True,
            'message': 'Trabajo finalizado exitosamente.',
            'nuevo_estado': asignacion.get_estado_display()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error al finalizar el trabajo: {str(e)}'
        })

@login_required
@csrf_protect
@require_http_methods(["POST"])
def cancelar_asignacion(request, asignacion_id):
    """
    Cancela una asignación específica.

    Reglas de cancelación:
    - Solo supervisores y administradores pueden cancelar
    - Supervisores solo pueden cancelar en su área
    - Administradores pueden cancelar cualquier asignación

    Args:
        request: HttpRequest object con datos del formulario
        asignacion_id: ID de la asignación a cancelar

    Returns:
        JsonResponse: Resultado de la operación para AJAX
        HttpResponse: Redirección para peticiones normales

    Security:
        - Requiere autenticación (@login_required)
        - Protección CSRF (@csrf_protect)
        - Solo métodos POST (@require_http_methods)
        - Validación de permisos por rol
    """
    user = request.user

    # Obtener asignación
    asignacion = get_object_or_404(
        AsignacionTicket.objects.select_related('ticket', 'ticket__grupo', 'usuario'),
        id=asignacion_id,
        is_active=True
    )

    # Verificar permisos
    if not _puede_asignar_ticket(user, asignacion.ticket):
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'No tienes permisos para cancelar esta asignación.'
            })
        messages.error(request, 'No tienes permisos para cancelar esta asignación.')
        return redirect('asignaciones:lista_asignaciones')

    # Obtener motivo de cancelación
    motivo = request.POST.get('motivo', '').strip()

    # Cancelar la asignación
    asignacion.cancelar_asignacion(usuario=user, motivo=motivo)

    # Registrar en historial
    HistorialTicket.registrar_cambio(
        ticket=asignacion.ticket,
        usuario=user,
        accion=f'Asignación cancelada para {asignacion.usuario.get_full_name() or asignacion.usuario.username}',
        detalles={
            'usuario_desasignado': asignacion.usuario.username,
            'usuario_desasignado_nombre': asignacion.usuario.get_full_name() or asignacion.usuario.username,
            'cancelado_por': user.username,
            'motivo': motivo if motivo else 'Sin motivo especificado'
        }
    )

    # Crear notificación para el usuario desasignado
    _crear_notificacion_desasignacion(asignacion.ticket, user, asignacion.usuario, motivo)

    # Respuesta
    mensaje = f'Asignación cancelada para {asignacion.usuario.get_full_name() or asignacion.usuario.username}'

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': mensaje,
            'usuario_desasignado': {
                'id': asignacion.usuario.id,
                'nombre': asignacion.usuario.get_full_name() or asignacion.usuario.username,
                'username': asignacion.usuario.username
            }
        })

    messages.success(request, mensaje)
    return redirect('asignaciones:lista_asignaciones')


@can_assign_tickets_required
@csrf_protect
@require_http_methods(["POST"])
def asignar_ticket(request, ticket_id):
    """
    Asigna un ticket a un usuario específico (compatible con tickets app).

    Esta función mantiene compatibilidad con las URLs existentes de tickets.

    Args:
        request: HttpRequest object con datos del formulario
        ticket_id: ID del ticket a asignar

    Returns:
        JsonResponse: Resultado de la operación para AJAX
        HttpResponse: Redirección para peticiones normales
    """
    # Agregar ticket_id al POST data para usar crear_asignacion
    mutable_post = request.POST.copy()
    mutable_post['ticket_id'] = ticket_id
    request.POST = mutable_post

    # Usar la función crear_asignacion existente
    response = crear_asignacion(request)

    # Si es una redirección exitosa, redirigir al detalle del ticket
    if hasattr(response, 'status_code') and response.status_code == 302:
        return redirect('tickets:detalle_ticket', ticket_id=ticket_id)

    return response


@login_required
@csrf_protect
@require_http_methods(["POST"])
def desasignar_ticket(request, ticket_id, usuario_id):
    """
    Desasigna un ticket de un usuario específico (compatible con tickets app).

    Esta función mantiene compatibilidad con las URLs existentes de tickets.

    Args:
        request: HttpRequest object con datos del formulario
        ticket_id: ID del ticket
        usuario_id: ID del usuario a desasignar

    Returns:
        JsonResponse: Resultado de la operación para AJAX
        HttpResponse: Redirección para peticiones normales
    """
    user = request.user

    # Obtener ticket
    ticket = get_object_or_404(
        Ticket.objects.select_related('creado_por', 'grupo'),
        id=ticket_id,
        is_active=True
    )

    # Verificar permisos
    if not _puede_asignar_ticket(user, ticket):
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'No tienes permisos para desasignar este ticket.'
            })
        messages.error(request, 'No tienes permisos para desasignar este ticket.')
        return redirect('tickets:detalle_ticket', ticket_id=ticket_id)

    # Obtener usuario y asignación
    try:
        usuario_desasignado = User.objects.get(id=usuario_id, is_active=True)
    except User.DoesNotExist:
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'Usuario no encontrado.'
            })
        messages.error(request, 'Usuario no encontrado.')
        return redirect('tickets:detalle_ticket', ticket_id=ticket_id)

    # Buscar asignación activa
    asignacion = AsignacionTicket.objects.filter(
        ticket=ticket,
        usuario=usuario_desasignado,
        is_active=True
    ).first()

    if not asignacion:
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': f'El ticket no está asignado a {usuario_desasignado.get_full_name() or usuario_desasignado.username}.'
            })
        messages.warning(request, f'El ticket no está asignado a {usuario_desasignado.get_full_name() or usuario_desasignado.username}.')
        return redirect('tickets:detalle_ticket', ticket_id=ticket_id)

    # Obtener motivo de desasignación
    motivo = request.POST.get('motivo', '').strip()

    # Cancelar la asignación
    asignacion.cancelar_asignacion(usuario=user, motivo=motivo)

    # Registrar en historial
    HistorialTicket.registrar_cambio(
        ticket=ticket,
        usuario=user,
        accion=f'Ticket desasignado de {usuario_desasignado.get_full_name() or usuario_desasignado.username}',
        detalles={
            'usuario_desasignado': usuario_desasignado.username,
            'usuario_desasignado_nombre': usuario_desasignado.get_full_name() or usuario_desasignado.username,
            'desasignado_por': user.username,
            'motivo': motivo if motivo else 'Sin motivo especificado'
        }
    )

    # Crear notificación para el usuario desasignado
    _crear_notificacion_desasignacion(ticket, user, usuario_desasignado, motivo)

    # Respuesta
    mensaje = f'Ticket #{ticket.id} desasignado de {usuario_desasignado.get_full_name() or usuario_desasignado.username}'

    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'success': True,
            'message': mensaje,
            'usuario_desasignado': {
                'id': usuario_desasignado.id,
                'nombre': usuario_desasignado.get_full_name() or usuario_desasignado.username,
                'username': usuario_desasignado.username
            }
        })

    messages.success(request, mensaje)
    return redirect('tickets:detalle_ticket', ticket_id=ticket_id)

def historial_asignaciones(request):
    return HttpResponse("Historial de asignaciones - En desarrollo")

def reportes_asignaciones(request):
    return HttpResponse("Reportes de asignaciones - En desarrollo")

def reporte_productividad(request):
    return HttpResponse("Reporte de productividad - En desarrollo")

def cambiar_estado_ajax(request):
    return HttpResponse("Cambiar estado AJAX - En desarrollo")

def obtener_usuarios_grupo(request):
    return HttpResponse("Obtener usuarios del grupo - En desarrollo")

@login_required
@require_http_methods(["GET"])
def estadisticas_usuario_ajax(request):
    """
    Obtiene estadísticas de un usuario específico para mostrar en modales.

    Args:
        request: HttpRequest object con usuario_id como parámetro

    Returns:
        JsonResponse: Estadísticas del usuario

    Security:
        - Requiere autenticación (@login_required)
        - Solo métodos GET (@require_http_methods)
    """
    user = request.user
    usuario_id = request.GET.get('usuario_id')

    if not usuario_id:
        return JsonResponse({
            'success': False,
            'message': 'ID de usuario requerido'
        })

    try:
        usuario = User.objects.get(id=usuario_id, is_active=True)
    except User.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'Usuario no encontrado'
        })

    # Obtener estadísticas del usuario
    asignaciones_activas = AsignacionTicket.objects.filter(
        usuario=usuario,
        is_active=True,
        estado__in=[1, 2]  # Asignado o En Progreso
    ).count()

    asignaciones_finalizadas = AsignacionTicket.objects.filter(
        usuario=usuario,
        is_active=True,
        estado=3  # Finalizado
    ).count()

    total_asignaciones = AsignacionTicket.objects.filter(
        usuario=usuario,
        is_active=True
    ).count()

    # Calcular tiempo promedio de resolución
    asignaciones_con_tiempo = AsignacionTicket.objects.filter(
        usuario=usuario,
        is_active=True,
        estado=3,
        fecha_finalizacion__isnull=False
    )

    tiempo_promedio = None
    if asignaciones_con_tiempo.exists():
        total_tiempo = 0
        count = 0
        for asignacion in asignaciones_con_tiempo:
            if asignacion.fecha_inicio:
                tiempo_resolucion = (asignacion.fecha_finalizacion - asignacion.fecha_inicio).total_seconds() / 3600  # horas
                total_tiempo += tiempo_resolucion
                count += 1

        if count > 0:
            tiempo_promedio = round(total_tiempo / count, 1)

    return JsonResponse({
        'success': True,
        'usuario': {
            'id': usuario.id,
            'nombre': usuario.get_full_name() or usuario.username,
            'email': usuario.email
        },
        'estadisticas': {
            'asignaciones_activas': asignaciones_activas,
            'asignaciones_finalizadas': asignaciones_finalizadas,
            'total_asignaciones': total_asignaciones,
            'tiempo_promedio_horas': tiempo_promedio
        }
    })
