"""
Comando para probar el rendimiento del sistema con los datos generados.

Uso:
    python manage.py test_rendimiento

Este comando:
- Mide tiempos de consulta en diferentes vistas
- Genera algunas asignaciones aleatorias
- Muestra estadísticas de rendimiento
"""

import time
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.db import connection
from django.test import RequestFactory
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.auth.middleware import AuthenticationMiddleware

from tickets.models import Ticket
from ciudadano.models import Ciudadano
from asignaciones.models import AsignacionTicket
from asignaciones.views import lista_asignaciones
from tickets.views import lista_tickets

User = get_user_model()


class Command(BaseCommand):
    help = 'Prueba el rendimiento del sistema con los datos generados'

    def add_arguments(self, parser):
        parser.add_argument(
            '--crear-asignaciones',
            action='store_true',
            help='Crear asignaciones aleatorias para testing'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 Iniciando pruebas de rendimiento...')
        )

        # Estadísticas iniciales
        self.mostrar_estadisticas_sistema()

        if options['crear_asignaciones']:
            self.crear_asignaciones_prueba()

        # Pruebas de rendimiento
        self.probar_consultas_tickets()
        self.probar_consultas_asignaciones()
        self.mostrar_consultas_sql()

        self.stdout.write(
            self.style.SUCCESS('\n✅ Pruebas de rendimiento completadas!')
        )

    def mostrar_estadisticas_sistema(self):
        """Muestra estadísticas actuales del sistema."""
        self.stdout.write('\n📊 Estadísticas del Sistema:')
        self.stdout.write(f'   - Tickets: {Ticket.objects.count()}')
        self.stdout.write(f'   - Ciudadanos: {Ciudadano.objects.count()}')
        self.stdout.write(f'   - Usuarios: {User.objects.count()}')
        self.stdout.write(f'   - Grupos/Áreas: {Group.objects.count()}')
        self.stdout.write(f'   - Asignaciones: {AsignacionTicket.objects.count()}')

    def crear_asignaciones_prueba(self):
        """Crea asignaciones aleatorias para testing."""
        import random
        
        self.stdout.write('\n🎯 Creando asignaciones de prueba...')
        
        # Obtener tickets sin asignar
        tickets_sin_asignar = Ticket.objects.filter(
            asignaciones__isnull=True,
            is_active=True
        )[:100]  # Máximo 100 asignaciones
        
        usuarios = list(User.objects.filter(is_active=True))
        asignaciones_creadas = 0
        
        for ticket in tickets_sin_asignar:
            if random.random() < 0.6:  # 60% de probabilidad
                try:
                    usuario_asignado = random.choice(usuarios)
                    AsignacionTicket.objects.create(
                        ticket=ticket,
                        usuario=usuario_asignado,
                        asignado_por=random.choice(usuarios),
                        estado=random.choice([1, 2]),  # Asignado o En Progreso
                        observaciones="Asignación generada para testing de rendimiento"
                    )
                    asignaciones_creadas += 1
                except Exception as e:
                    continue
        
        self.stdout.write(f'   ✅ Asignaciones creadas: {asignaciones_creadas}')

    def probar_consultas_tickets(self):
        """Prueba el rendimiento de las consultas de tickets."""
        self.stdout.write('\n🎫 Probando rendimiento de tickets...')
        
        # Reset query count
        connection.queries_log.clear()
        
        # Simular consulta de lista de tickets
        inicio = time.time()
        
        tickets = Ticket.objects.select_related(
            'creado_por', 'grupo'
        ).prefetch_related(
            'ciudadanos__ciudadano',
            'asignaciones__usuario'
        ).filter(is_active=True)[:20]
        
        # Forzar evaluación del queryset
        list(tickets)
        
        fin = time.time()
        tiempo_consulta = (fin - inicio) * 1000  # En milisegundos
        
        self.stdout.write(f'   ⏱️  Tiempo consulta tickets: {tiempo_consulta:.2f}ms')
        self.stdout.write(f'   📊 Consultas SQL ejecutadas: {len(connection.queries)}')

    def probar_consultas_asignaciones(self):
        """Prueba el rendimiento de las consultas de asignaciones."""
        self.stdout.write('\n📋 Probando rendimiento de asignaciones...')
        
        # Reset query count
        connection.queries_log.clear()
        
        inicio = time.time()
        
        # Simular consulta optimizada de asignaciones
        asignaciones = AsignacionTicket.objects.select_related(
            'ticket', 
            'ticket__grupo', 
            'ticket__creado_por',
            'usuario', 
            'usuario__cargo',
            'asignado_por'
        ).prefetch_related(
            'ticket__imagenes',
            'ticket__ciudadanos__ciudadano'
        ).filter(is_active=True)[:15]
        
        # Forzar evaluación
        list(asignaciones)
        
        fin = time.time()
        tiempo_consulta = (fin - inicio) * 1000
        
        self.stdout.write(f'   ⏱️  Tiempo consulta asignaciones: {tiempo_consulta:.2f}ms')
        self.stdout.write(f'   📊 Consultas SQL ejecutadas: {len(connection.queries)}')

    def mostrar_consultas_sql(self):
        """Muestra información sobre las consultas SQL más recientes."""
        if not connection.queries:
            return
            
        self.stdout.write('\n🔍 Análisis de consultas SQL:')
        
        tiempos = [float(q['time']) for q in connection.queries[-10:]]  # Últimas 10
        if tiempos:
            tiempo_total = sum(tiempos) * 1000
            tiempo_promedio = (sum(tiempos) / len(tiempos)) * 1000
            tiempo_max = max(tiempos) * 1000
            
            self.stdout.write(f'   📊 Últimas {len(tiempos)} consultas:')
            self.stdout.write(f'      - Tiempo total: {tiempo_total:.2f}ms')
            self.stdout.write(f'      - Tiempo promedio: {tiempo_promedio:.2f}ms')
            self.stdout.write(f'      - Tiempo máximo: {tiempo_max:.2f}ms')
            
            # Mostrar consultas más lentas
            consultas_lentas = [q for q in connection.queries[-10:] if float(q['time']) > 0.01]
            if consultas_lentas:
                self.stdout.write(f'   ⚠️  Consultas lentas (>10ms): {len(consultas_lentas)}')

    def benchmark_paginacion(self):
        """Prueba el rendimiento de la paginación."""
        self.stdout.write('\n📄 Probando rendimiento de paginación...')
        
        from django.core.paginator import Paginator
        
        # Probar diferentes tamaños de página
        tamaños = [10, 15, 20, 25, 50]
        
        for tamaño in tamaños:
            inicio = time.time()
            
            queryset = Ticket.objects.select_related('creado_por', 'grupo').filter(is_active=True)
            paginator = Paginator(queryset, tamaño)
            page = paginator.get_page(1)
            
            # Forzar evaluación
            list(page)
            
            fin = time.time()
            tiempo = (fin - inicio) * 1000
            
            self.stdout.write(f'   📄 Página de {tamaño} items: {tiempo:.2f}ms')

    def probar_filtros(self):
        """Prueba el rendimiento de diferentes filtros."""
        self.stdout.write('\n🔍 Probando rendimiento de filtros...')
        
        filtros_prueba = [
            ('Por estado', {'estado': 1}),
            ('Por prioridad', {'prioridad': 'alta'}),
            ('Por grupo', {'grupo__name__icontains': 'Fontanería'}),
            ('Por fecha', {'fecha_creacion__gte': '2024-01-01'}),
            ('Búsqueda texto', {'titulo__icontains': 'agua'}),
        ]
        
        for nombre, filtro in filtros_prueba:
            inicio = time.time()
            
            tickets = Ticket.objects.filter(**filtro, is_active=True)[:20]
            count = tickets.count()
            list(tickets)  # Forzar evaluación
            
            fin = time.time()
            tiempo = (fin - inicio) * 1000
            
            self.stdout.write(f'   🔍 {nombre}: {tiempo:.2f}ms ({count} resultados)')
