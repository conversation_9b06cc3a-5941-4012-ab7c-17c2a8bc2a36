# Análisis completo — Sistema de Tickets Municipal
**Fecha:** 2025-01-16  
**Autor del análisis:** Augment Agent (<PERSON> 4)

## 1. Resumen ejecutivo

El **Sistema de Tickets Municipal** es una aplicación web desarrollada en Django 5.2.3 para la gestión integral de solicitudes ciudadanas en la Municipalidad de Estanzuela, Zacapa. Los ciudadanos acuden presencialmente a la municipalidad, donde el personal autorizado (secretaria) recibe su queja o solicitud y la registra en el sistema generando un ticket correspondiente.

Al finalizar el registro, se entrega al ciudadano un comprobante impreso con un código QR, el cual, al ser escaneado, permite consultar en línea el estado y el progreso de su solicitud, brindándole transparencia y seguimiento sin necesidad de acceso directo al sistema. Mientras tanto, el personal municipal gestiona, asigna y resuelve los tickets de manera eficiente desde la plataforma.

**Problema que resuelve:** Centraliza y digitaliza la gestión de solicitudes ciudadanas, eliminando procesos manuales y mejorando la transparencia y eficiencia en la atención al ciudadano.

**Público objetivo:** Ciudadanos de Estanzuela (solicitantes), personal municipal (secretarias, supervisores, empleados) y administradores del sistema.

**Estado general:** Sistema maduro y funcional con arquitectura robusta, implementación completa de seguridad, sistema de permisos granular, y módulos especializados para reportes y notificaciones. Incluye características avanzadas como consulta pública de tickets, sistema de advertencias de seguridad y generación de reportes en PDF/Excel.

## 2. Cómo levantar el proyecto

### Requisitos previos
- Python 3.8+
- MySQL 5.7+ o MariaDB 10.3+
- Sistema operativo: Windows/Linux/macOS
- Variables de entorno configuradas (ver archivo .env.example)

### Comandos de instalación
```bash
# 1. Clonar repositorio y crear entorno virtual
git clone <repository_url>
cd tickets
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 2. Instalar dependencias
pip install -r requirements.txt

# 3. Configurar base de datos MySQL
mysql -u root -p
CREATE DATABASE tickets_municipal;
CREATE USER 'tickets_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON tickets_municipal.* TO 'tickets_user'@'localhost';

# 4. Configurar variables de entorno
cp .env.example .env
# Editar .env con configuración de BD y SECRET_KEY

# 5. Ejecutar migraciones
python manage.py migrate

# 6. Crear superusuario
python manage.py createsuperuser


# 7. Ejecutar servidor de desarrollo
python manage.py runserver
```

### Comandos de testing
```bash
# Ejecutar todas las pruebas
python manage.py test

# Verificar configuración
python manage.py check --deploy

# Verificar migraciones
python manage.py showmigrations
```

## 3. Stack y dependencias

### Lenguajes y frameworks
- **Backend:** Python 3.8+ con Django 5.2.3
- **Base de datos:** MySQL con PyMySQL 1.1.1 y mysqlclient 2.2.7
- **Frontend:** HTML5, CSS3, JavaScript, Bootstrap 5
- **Plantillas:** Django Templates con Crispy Forms

### Dependencias principales
```
Django==5.2.3
mysqlclient==2.2.7
PyMySQL==1.1.1
django-crispy-forms==2.4
crispy-bootstrap5==2025.6
django-ratelimit==4.1.0
python-decouple==3.8
reportlab==4.4.2
openpyxl==3.1.5
pillow==11.2.1
qrcode==8.0
requests==2.32.4
```

### Archivos clave
- `core/settings.py` - Configuración principal con seguridad avanzada
- `requirements.txt` - Dependencias del proyecto
- `manage.py` - Punto de entrada de Django

## 4. Estructura del repositorio

```
tickets/
├── core/                    # Configuración principal Django
├── Base/                    # App base con middleware de seguridad
├── Home/                    # Dashboard principal
├── login_app/               # Sistema de autenticación
├── user/                    # Gestión de usuarios extendidos
├── tickets/                 # Gestión de tickets (core)
├── ciudadano/               # Gestión de ciudadanos
├── asignaciones/            # Asignación de tickets a empleados
├── notificaciones/          # Sistema de notificaciones
├── public_tickets/          # Consulta pública sin autenticación
├── reportes/                # Generación de reportes PDF/Excel
├── permissions/             # Sistema centralizado de permisos
├── templates/               # Templates globales (403, 404, 500)
├── logs/                    # Archivos de log del sistema
├── docs/                    # Documentación técnica
└── media/                   # Archivos subidos por usuarios
```

## 5. Funcionalidades (lista detallada)

### 5.1 Gestión de Tickets
- **Descripción:** CRUD completo de tickets con estados, prioridades y asignaciones
- **Usuario objetivo:** Secretarias (crear), Supervisores y Empleados (gestionar)
- **Endpoints:** `/tickets/`, `/tickets/crear/`, `/tickets/<id>/`
- **Archivos clave:** `tickets/models.py:22-652`, `tickets/views.py`
- **Evidencia:** Modelo Ticket con validaciones, estados (Abierto, En Progreso, Cerrado, Pendiente)

### 5.2 Sistema de Usuarios Extendidos
- **Descripción:** Gestión de usuarios con cargos, teléfonos y familiares
- **Usuario objetivo:** Administradores
- **Endpoints:** `/usuarios/`, `/usuarios/crear/`
- **Archivos clave:** `user/models.py:31-151`
- **Evidencia:** Modelo User extendido con cargo, is_supervisor, DPI, género

### 5.3 Gestión de Ciudadanos
- **Descripción:** Registro y gestión de ciudadanos solicitantes
- **Usuario objetivo:** Secretarias, Supervisores
- **Endpoints:** `/ciudadanos/`, `/ciudadanos/registrar/`
- **Archivos clave:** `ciudadano/models.py:15-223`
- **Evidencia:** Validación DPI guatemalteco, relación con tickets

### 5.4 Sistema de Asignaciones
- **Descripción:** Asignación de tickets a empleados específicos con seguimiento
- **Usuario objetivo:** Supervisores, Administradores
- **Endpoints:** `/asignaciones/`, `/asignaciones/asignar/`
- **Archivos clave:** `asignaciones/models.py:15-292`
- **Evidencia:** Estados de asignación, fechas de inicio/fin, sincronización automática

### 5.5 Sistema de Notificaciones
- **Descripción:** Notificaciones a usuarios individuales y grupos
- **Usuario objetivo:** Administradores, Supervisores, Secretarias
- **Endpoints:** `/notificaciones/`, `/notificaciones/crear/`
- **Archivos clave:** `notificaciones/models.py:18-294`
- **Evidencia:** Tipos de notificación, seguimiento de lectura, notificaciones masivas

### 5.6 Consulta Pública de Tickets
- **Descripción:** Consulta de estado de tickets sin autenticación mediante token
- **Usuario objetivo:** Ciudadanos (público general)
- **Endpoints:** `/consulta/`, `/ticket/<token>/`
- **Archivos clave:** `public_tickets/`, `tickets/models.py:114-120`
- **Evidencia:** Token único por ticket, acceso público sin login

### 5.7 Sistema de Reportes
- **Descripción:** Generación de reportes PDF/Excel por empleado, área, ciudadano
- **Usuario objetivo:** Administradores, Superadministradores
- **Endpoints:** `/reportes/`, `/reportes/empleado/`, `/reportes/area/`
- **Archivos clave:** `reportes/models.py`, `reportes/generators/`
- **Evidencia:** Múltiples formatos, filtros de fecha, auditoría de reportes generados

### 5.8 Sistema de Permisos
- **Descripción:** Control de acceso basado en roles con permisos específicos
- **Usuario objetivo:** Sistema (todos los usuarios)
- **Archivos clave:** `permissions/core.py:13-517`
- **Evidencia:** Roles (Admin, Supervisor, Secretaria, Empleado), permisos por funcionalidad

### 5.9 Sistema de Seguridad
- **Descripción:** Middleware de seguridad, rate limiting, advertencias progresivas
- **Usuario objetivo:** Sistema (protección general)
- **Archivos clave:** `Base/middleware.py:1-422`, `Base/models.py:9-64`
- **Evidencia:** Detección de ataques, CSP, headers de seguridad, logging de intentos

## 6. Flujos de usuario y UX

```mermaid
graph TD
    A[Ciudadano] --> B[Solicita Servicio]
    B --> C[Secretaria Crea Ticket]
    C --> D[Asigna a Grupo/Área]
    D --> E[Supervisor Asigna Empleado]
    E --> F[Empleado Trabaja Ticket]
    F --> G[Cambia Estado]
    G --> H{¿Resuelto?}
    H -->|Sí| I[Cierra Ticket]
    H -->|No| F
    I --> J[Ciudadano Consulta Estado]
    
    K[Admin] --> L[Genera Reportes]
    K --> M[Gestiona Usuarios]
    K --> N[Ve Estadísticas Seguridad]
```

## 7. Endpoints (tabla)

| Método | Ruta | Autenticación | Descripción | Archivos |
|--------|------|---------------|-------------|----------|
| GET/POST | `/` | No | Login de usuarios | `login_app/views.py` |
| GET | `/inicio/` | Sí | Dashboard principal | `Home/views.py` |
| GET/POST | `/tickets/` | Sí | Lista y creación de tickets | `tickets/views.py` |
| GET/POST | `/ciudadanos/` | Secretaria+ | Gestión de ciudadanos | `ciudadano/views.py` |
| GET/POST | `/usuarios/` | Admin | Gestión de usuarios | `user/views.py` |
| GET/POST | `/asignaciones/` | Supervisor+ | Gestión de asignaciones | `asignaciones/views.py` |
| GET/POST | `/notificaciones/` | Sí | Sistema de notificaciones | `notificaciones/views.py` |
| GET | `/consulta/<token>/` | No | Consulta pública de tickets | `public_tickets/views.py` |
| GET/POST | `/reportes/` | Admin | Generación de reportes | `reportes/views.py` |
| GET | `/admin/` | Staff | Panel de administración Django | Django Admin |

## 8. Modelo de datos / ERD

```mermaid
erDiagram
    User ||--o{ Ticket : "creado_por"
    User ||--o{ AsignacionTicket : "usuario"
    User ||--o{ CelularUsuario : "usuario"
    User }o--|| CargoUsuario : "cargo"
    
    Ciudadano ||--o{ CiudadanoTicket : "ciudadano"
    Ticket ||--|| CiudadanoTicket : "ticket"
    
    Ticket ||--o{ AsignacionTicket : "ticket"
    Ticket ||--o{ HistorialTicket : "ticket"
    Ticket ||--o{ TicketImagen : "ticket"
    Ticket }o--|| Group : "grupo"
    
    Notificacion ||--o{ NotificacionUsuario : "notificacion"
    Notificacion ||--o{ NotificacionGrupo : "notificacion"
    
    User ||--o{ ReporteGenerado : "generado_por"
```

### Entidades principales:
- **User:** Usuario extendido con cargo, DPI, is_supervisor
- **Ticket:** Ticket principal con estado, prioridad, token público
- **Ciudadano:** Ciudadano solicitante con validación DPI
- **AsignacionTicket:** Asignación de tickets a empleados
- **Notificacion:** Sistema de notificaciones
- **ReporteGenerado:** Auditoría de reportes generados

## 9. Arquitectura técnica

### Componentes principales:
1. **Aplicación Django:** Servidor web principal
2. **Base de datos MySQL:** Almacenamiento persistente
3. **Sistema de archivos:** Media files (imágenes de tickets)
4. **Cache local:** Para rate limiting
5. **Sistema de logs:** Archivos rotativos de auditoría

### Stack tecnológico:
- **Web Framework:** Django 5.2.3
- **Base de datos:** MySQL con conexiones optimizadas
- **Cache:** Local memory cache para rate limiting
- **Seguridad:** Middleware personalizado, CSP, HTTPS
- **Archivos estáticos:** Servidos por Django (desarrollo)
- **Generación de reportes:** ReportLab (PDF) + OpenPyXL (Excel)

### Integraciones externas:
- **CDNs:** Bootstrap, jQuery, FontAwesome desde CDN
- **Sin APIs externas:** Sistema completamente autónomo
- **Sin servicios de email:** Deshabilitado por decisión del cliente

## 10. Pruebas y calidad

### Pruebas implementadas:
testing modelos, vistas, forms, permisos, seguridad, reportes, notificaciones, integración de apps de forma manual

### Herramientas de calidad:
- Django's built-in testing framework
- Validaciones de modelo con Django validators
- Middleware de seguridad personalizado
- Logging extensivo para auditoría

## 11. Seguridad y privacidad

### Medidas implementadas:
1. **Autenticación robusta:** Contraseñas de 12+ caracteres
2. **Sistema de permisos granular:** Control por rol y funcionalidad
3. **Middleware de seguridad:** Rate limiting, detección de ataques
4. **Headers de seguridad:** CSP, XSS protection, HSTS
5. **Advertencias progresivas:** Sistema de 3 intentos antes de deslogueo
6. **Logging de seguridad:** Auditoría completa de intentos no autorizados
7. **Validación de datos:** Sanitización de inputs, validadores personalizados
8. **Tokens únicos:** Para acceso público a tickets


## 12. Observabilidad y CI/CD

### Logging implementado:
- **Archivos:** `logs/django.log`, `logs/security.log`
- **Rotación:** 5MB por archivo, 5-10 backups
- **Niveles:** INFO, WARNING, ERROR, CRITICAL
- **Contenido:** Requests, errores, intentos de seguridad

### Monitoreo:
- **Panel de estadísticas:** `/base/security-stats/` (solo admin)
- **Métricas:** Intentos no autorizados, IPs sospechosas, URLs atacadas
- **Auto-refresh:** Cada 30 segundos

### CI/CD:
- **No implementado:** Sin pipelines automáticos detectados
- **Deploy semi manual:** Documentado en `INSTALACION_PRODUCCION.md`, se tiene un deploy semi automatico el cual se ejcuta manualmente por el administrador del servidor 

### Comandos de diagnóstico:
```bash
# Verificar estado
python manage.py check --deploy

# Ver migraciones
python manage.py showmigrations

# Limpiar logs antiguos
python manage.py cleanup_unauthorized_attempts --days 30
```


## 13. Anexos

### Diagrama de arquitectura simplificado:
```
[Ciudadano] --> [Consulta Pública] --> [Base de Datos]
     |
[Secretaria] --> [Sistema Web] --> [Middleware Seguridad] --> [Base de Datos]
     |              |
[Supervisor] -------+
     |              |
[Empleado] ---------+
     |              |
[Admin] ------------> [Panel Admin] --> [Reportes/Logs]
```

### Tecnologías por capa:
- **Presentación:** HTML5, Bootstrap 5, JavaScript, Django Templates
- **Lógica:** Django 5.2.3, Python 3.8+
- **Datos:** MySQL 5.7+, Django ORM
- **Seguridad:** Middleware personalizado, Django Security
- **Infraestructura:** Servidor web (desarrollo: runserver)

---

**Sistema robusto y bien estructurado, listo para producción con recomendaciones menores de mejora.**

*Municipalidad de Estanzuela - Un Gobierno de puertas abiertas*
