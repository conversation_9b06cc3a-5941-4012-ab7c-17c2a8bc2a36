{% extends 'Base/base.html' %}
{% block title %}Usuarios{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h2>Usuarios</h2>
    {% if perms.user.add_user %}
    <a href="{% url 'user:crear_usuario' %}" class="btn btn-primary">Nuevo Usuario</a>
    {% endif %}
</div>
<form class="row g-2 mb-3" method="get" id="filtros-form">
    <div class="col-md-3">
        <input type="text" name="q" class="form-control" placeholder="Buscar por nombre, usuario o DPI" value="{{ busqueda }}">
    </div>
    <div class="col-md-3">
        <select name="cargo" class="form-select">
            <option value="">Todos los cargos</option>
            {% for cargo in cargos %}
                <option value="{{ cargo.id }}" {% if request.GET.cargo == cargo.id|stringformat:"s" %}selected{% endif %}>{{ cargo.nombre }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="col-md-3">
        <select name="area" class="form-select">
            <option value="">Todas las áreas</option>
            {% for area in areas %}
                <option value="{{ area.id }}" {% if request.GET.area == area.id|stringformat:"s" %}selected{% endif %}>{{ area.name }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="col-md-2">
        <button type="submit" class="btn btn-outline-secondary w-100">Filtrar</button>
    </div>
</form>
<div id="usuarios-lista">
    {% include 'user/parciales/lista_usuarios.html' %}
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    let page = 2;
    let loading = false;
    window.onscroll = function() {
        if ((window.innerHeight + window.scrollY) >= document.body.offsetHeight - 200 && !loading) {
            loading = true;
            fetch(`?page=${page}&q={{ busqueda }}&cargo={{ request.GET.cargo }}&area={{ request.GET.area }}`, {
                headers: {'x-requested-with': 'XMLHttpRequest'}
            })
            .then(response => response.json())
            .then(data => {
                if (data.html) {
                    document.getElementById('usuarios-lista').insertAdjacentHTML('beforeend', data.html);
                    if (data.has_next) {
                        page += 1;
                        loading = false;
                    }
                }
            });
        }
    };
});
</script>
{% endblock %}