# Instrucciones para Reproducir el Entorno - Sistema de Tickets Municipal

## Requisitos del Sistema

### Software requerido:
- **Python:** 3.8 o superior
- **Base de datos:** MySQL 5.7+ o MariaDB 10.3+
- **Sistema operativo:** Windows 10+, Ubuntu 18.04+, macOS 10.14+
- **Memoria RAM:** Mínimo 4GB, recomendado 8GB
- **Espacio en disco:** Mínimo 2GB libres

### Herramientas adicionales:
- Git (para clonar el repositorio)
- Editor de texto (VS Code, PyCharm, etc.)
- Cliente MySQL (MySQL Workbench, phpMyAdmin, etc.)

## Paso 1: Preparación del Entorno

### 1.1 Instalar Python y dependencias del sistema

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
sudo apt install mysql-server mysql-client
sudo apt install libmysqlclient-dev pkg-config
```

**Windows:**
- <PERSON>cargar Python desde https://python.org
- Instalar MySQL desde https://dev.mysql.com/downloads/installer/
- Instalar Microsoft Visual C++ Build Tools

**macOS:**
```bash
brew install python3 mysql
brew install mysql-client pkg-config
```

### 1.2 Configurar MySQL

```sql
-- Conectar como root
mysql -u root -p

-- Crear base de datos
CREATE DATABASE tickets_municipal CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Crear usuario específico
CREATE USER 'tickets_user'@'localhost' IDENTIFIED BY 'tickets_password_2024';

-- Otorgar permisos
GRANT ALL PRIVILEGES ON tickets_municipal.* TO 'tickets_user'@'localhost';
FLUSH PRIVILEGES;

-- Verificar creación
SHOW DATABASES;
SELECT User, Host FROM mysql.user WHERE User = 'tickets_user';
```

## Paso 2: Configuración del Proyecto

### 2.1 Clonar y configurar entorno virtual

```bash
# Clonar repositorio (ajustar URL según corresponda)
git clone <URL_DEL_REPOSITORIO> tickets-municipal
cd tickets-municipal

# Crear entorno virtual
python3 -m venv venv

# Activar entorno virtual
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# Verificar activación (debe mostrar la ruta del venv)
which python
```

### 2.2 Instalar dependencias

```bash
# Actualizar pip
pip install --upgrade pip

# Instalar dependencias del proyecto
pip install -r requirements.txt

# Verificar instalación crítica
pip list | grep -E "(Django|mysql|PyMySQL)"
```

### 2.3 Configurar variables de entorno

Crear archivo `.env` en la raíz del proyecto:

```bash
# Copiar archivo de ejemplo si existe
cp .env.example .env

# O crear manualmente
touch .env
```

Contenido del archivo `.env`:
```env
# Configuración de Django
DEBUG=True
SECRET_KEY=tu-clave-secreta-muy-larga-y-segura-aqui-cambiar-en-produccion
ENVIRONMENT=development

# Hosts permitidos (separados por coma)
ALLOWED_HOSTS=localhost,127.0.0.1

# Orígenes CSRF confiables
CSRF_TRUSTED_ORIGINS=http://localhost:8000,http://127.0.0.1:8000

# Configuración de base de datos
DB_ENGINE=django.db.backends.mysql
DB_NAME=tickets_municipal
DB_USER=tickets_user
DB_PASSWORD=tickets_password_2024
DB_HOST=localhost
DB_PORT=3306

# URLs estáticas
STATIC_URL=/static/
```

## Paso 3: Inicialización de la Base de Datos

### 3.1 Ejecutar migraciones

```bash
# Verificar configuración
python manage.py check

# Crear migraciones si es necesario
python manage.py makemigrations

# Aplicar migraciones
python manage.py migrate

# Verificar migraciones aplicadas
python manage.py showmigrations
```

### 3.2 Crear datos iniciales

```bash
# Crear superusuario
python manage.py createsuperuser
# Seguir las instrucciones en pantalla

# Cargar datos iniciales si existen fixtures
python manage.py loaddata initial_data.json  # Si existe

# Crear grupos y permisos básicos
python manage.py shell
```

En el shell de Django:
```python
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType

# Crear grupos básicos
admin_group, created = Group.objects.get_or_create(name='Admin')
secretaria_group, created = Group.objects.get_or_create(name='Secretaria')
supervisor_group, created = Group.objects.get_or_create(name='Supervisor')
empleado_group, created = Group.objects.get_or_create(name='Empleado')

# Crear grupos de áreas
areas = ['Fontanería', 'Electricidad', 'Mantenimiento', 'Limpieza', 'Jardinería']
for area in areas:
    Group.objects.get_or_create(name=area)

print("Grupos creados exitosamente")
exit()
```

## Paso 4: Instalación de Módulos Opcionales

### 4.1 Sistema de Reportes

```bash
# Ejecutar script de instalación automática
python install_reportes.py

# Verificar instalación
python manage.py check
python manage.py showmigrations reportes
```

### 4.2 Verificar permisos

```bash
python manage.py shell
```

```python
from permissions.core import PermissionHelper
from django.contrib.auth import get_user_model

User = get_user_model()
admin_user = User.objects.filter(is_superuser=True).first()

if admin_user:
    print(f"Usuario admin: {admin_user.username}")
    print(f"Es admin: {PermissionHelper.is_admin(admin_user)}")
    print("Permisos de sidebar:", PermissionHelper.get_sidebar_permissions(admin_user))
else:
    print("No hay superusuarios creados")

exit()
```

## Paso 5: Ejecutar el Servidor

### 5.1 Servidor de desarrollo

```bash
# Ejecutar servidor
python manage.py runserver

# O especificar puerto
python manage.py runserver 8000

# Para acceso desde red local
python manage.py runserver 0.0.0.0:8000
```

### 5.2 Verificar funcionamiento

Abrir navegador y visitar:
- **Aplicación principal:** http://localhost:8000
- **Panel de administración:** http://localhost:8000/admin/
- **Dashboard:** http://localhost:8000/inicio/ (después de login)

## Paso 6: Configuración de Datos de Prueba

### 6.1 Crear usuarios de prueba

```bash
python manage.py shell
```

```python
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from user.models import CargoUsuario

User = get_user_model()

# Crear cargos
admin_cargo, _ = CargoUsuario.objects.get_or_create(nombre='Administrador')
secretaria_cargo, _ = CargoUsuario.objects.get_or_create(nombre='Secretaria')
supervisor_cargo, _ = CargoUsuario.objects.get_or_create(nombre='Supervisor')
empleado_cargo, _ = CargoUsuario.objects.get_or_create(nombre='Empleado')

# Crear usuarios de prueba
secretaria = User.objects.create_user(
    username='secretaria1',
    password='secretaria123',
    first_name='María',
    last_name='González',
    email='<EMAIL>',
    dpi='1234567890123',
    cargo=secretaria_cargo
)
secretaria.groups.add(Group.objects.get(name='Secretaria'))

supervisor = User.objects.create_user(
    username='supervisor1',
    password='supervisor123',
    first_name='Carlos',
    last_name='Rodríguez',
    email='<EMAIL>',
    dpi='2345678901234',
    cargo=supervisor_cargo,
    is_supervisor=True
)
supervisor.groups.add(Group.objects.get(name='Fontanería'))

empleado = User.objects.create_user(
    username='empleado1',
    password='empleado123',
    first_name='José',
    last_name='Martínez',
    email='<EMAIL>',
    dpi='3456789012345',
    cargo=empleado_cargo
)
empleado.groups.add(Group.objects.get(name='Fontanería'))

print("Usuarios de prueba creados:")
print("- secretaria1 / secretaria123")
print("- supervisor1 / supervisor123")
print("- empleado1 / empleado123")

exit()
```

### 6.2 Crear ciudadanos de prueba

```bash
python manage.py shell
```

```python
from ciudadano.models import Ciudadano

# Crear ciudadanos de prueba
ciudadano1 = Ciudadano.objects.create(
    dpi='1234567890123',
    nombre_completo='Ana María López',
    direccion='Zona 1, Estanzuela',
    telefono='12345678',
    email='<EMAIL>'
)

ciudadano2 = Ciudadano.objects.create(
    dpi='2345678901234',
    nombre_completo='Pedro Hernández',
    direccion='Zona 2, Estanzuela',
    telefono='23456789',
    email='<EMAIL>'
)

print("Ciudadanos de prueba creados")
exit()
```

## Paso 7: Verificación Final

### 7.1 Lista de verificación

- [ ] Servidor ejecutándose sin errores
- [ ] Login funciona con superusuario
- [ ] Dashboard carga correctamente
- [ ] Usuarios de prueba pueden hacer login
- [ ] Sidebar muestra opciones según rol
- [ ] Panel de admin accesible
- [ ] Base de datos conectada correctamente

### 7.2 Comandos de diagnóstico

```bash
# Verificar configuración para producción
python manage.py check --deploy

# Ver estado de migraciones
python manage.py showmigrations

# Verificar archivos estáticos
python manage.py collectstatic --dry-run

# Verificar permisos de archivos
ls -la logs/
ls -la media/
```

### 7.3 Solución de problemas comunes

**Error de conexión MySQL:**
```bash
# Verificar servicio MySQL
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# Verificar conexión
mysql -u tickets_user -p tickets_municipal
```

**Error de dependencias:**
```bash
# Reinstalar dependencias
pip install --force-reinstall -r requirements.txt

# Verificar versión Python
python --version
```

**Error de permisos:**
```bash
# Verificar permisos de directorios
chmod 755 logs/
chmod 755 media/
```

## Paso 8: Configuración para Desarrollo

### 8.1 Configuración de IDE

**VS Code - extensiones recomendadas:**
- Python
- Django
- MySQL
- GitLens

**PyCharm - configuración:**
- Configurar intérprete Python del venv
- Configurar Django settings
- Configurar base de datos

### 8.2 Variables de entorno para desarrollo

```env
# Agregar al .env para desarrollo
DEBUG=True
DJANGO_LOG_LEVEL=DEBUG

# Para debugging
DJANGO_SETTINGS_MODULE=core.settings
```

### 8.3 Comandos útiles para desarrollo

```bash
# Crear nueva migración
python manage.py makemigrations app_name

# Ejecutar shell con imports automáticos
python manage.py shell_plus  # Si django-extensions está instalado

# Ejecutar servidor con recarga automática
python manage.py runserver --noreload  # Sin recarga
python manage.py runserver --nothreading  # Sin threading

# Limpiar sesiones expiradas
python manage.py clearsessions

# Verificar URLs
python manage.py show_urls  # Si django-extensions está instalado
```

## Contacto y Soporte

Para problemas durante la instalación:

1. **Verificar logs:** `logs/django.log`
2. **Revisar configuración:** Archivo `.env`
3. **Consultar documentación:** Archivos README en el proyecto
4. **Verificar dependencias:** `pip list`

**El sistema debería estar funcionando completamente después de seguir estos pasos.**

---

*Municipalidad de Estanzuela - Un Gobierno de puertas abiertas*
