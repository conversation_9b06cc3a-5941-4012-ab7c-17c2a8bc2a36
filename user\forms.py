"""
user/forms.py
Formularios para la gestión de usuarios y su información relacionada.
Incluye validaciones personalizadas y widgets mejorados.
"""
"""
user/forms.py
Formularios para la gestión de usuarios con validaciones de seguridad.

Incluye formularios por pasos para:
- Información básica del usuario
- Teléfonos del usuario
- Información de familiares
- Teléfonos de emergencia
"""

from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import Group
from django.core.validators import RegexValidator
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Submit, Row, Column, HTML, Div
from crispy_forms.bootstrap import FormActions
from .models import User, CargoUsuario, CelularUsuario, Familiar, Parentesco, CelularEmergencia
import re


class UsuarioBasicoForm(UserCreationForm):
    """
    Formulario para el paso 1: Información básica del usuario.
    """

    # Validador para DPI guatemalteco
    dpi_validator = RegexValidator(
        regex=r'^\d{13}$',
        message='El DPI debe tener exactamente 13 dígitos numéricos.'
    )

    # Campos adicionales
    first_name = forms.CharField(
        max_length=30,
        required=True,
        label='Nombres',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Ingresa los nombres'
        })
    )

    last_name = forms.CharField(
        max_length=150,
        required=True,
        label='Apellidos',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Ingresa los apellidos'
        })
    )

    email = forms.EmailField(
        required=True,
        label='Correo Electrónico',
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )

    dpi = forms.CharField(
        max_length=20,
        validators=[dpi_validator],
        required=True,
        label='DPI',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '1234567890123',
            'pattern': r'\d{13}',
            'title': 'Ingresa 13 dígitos numéricos'
        })
    )

    fecha_nacimiento = forms.DateField(
        required=False,
        label='Fecha de Nacimiento',
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )

    genero = forms.ChoiceField(
        choices=[('', 'Selecciona...')] + list(User.GENERO_CHOICES),
        required=False,
        label='Género',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    cargo = forms.ModelChoiceField(
        queryset=CargoUsuario.objects.filter(is_active=True),
        required=False,
        label='Cargo',
        empty_label='Selecciona un cargo...',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    grupo = forms.ModelChoiceField(
        queryset=Group.objects.all(),
        required=True,
        label='Área/Departamento',
        empty_label='Selecciona el área...',
        widget=forms.Select(attrs={'class': 'form-select'}),
        help_text='Área o departamento donde trabajará el usuario'
    )

    is_supervisor = forms.BooleanField(
        required=False,
        label='¿Es Supervisor del Área?',
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text='Marca si este usuario supervisará a otros empleados en su área'
    )

    class Meta:
        model = User
        fields = [
            'username', 'first_name', 'last_name', 'email', 'dpi',
            'fecha_nacimiento', 'genero', 'cargo', 'grupo', 'is_supervisor',
            'password1', 'password2'
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Configurar Crispy Forms
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.attrs = {'novalidate': ''}

        self.helper.layout = Layout(
            HTML('<div class="step-header mb-4"><h4><i class="fas fa-user me-2"></i>Paso 1: Información Básica</h4></div>'),

            Fieldset(
                'Datos de Acceso',
                Row(
                    Column('username', css_class='col-md-6'),
                    Column('email', css_class='col-md-6'),
                ),
                Row(
                    Column('password1', css_class='col-md-6'),
                    Column('password2', css_class='col-md-6'),
                ),
                css_class='mb-4'
            ),

            Fieldset(
                'Información Personal',
                Row(
                    Column('first_name', css_class='col-md-6'),
                    Column('last_name', css_class='col-md-6'),
                ),
                Row(
                    Column('dpi', css_class='col-md-6'),
                    Column('fecha_nacimiento', css_class='col-md-6'),
                ),
                'genero',
                css_class='mb-4'
            ),

            Fieldset(
                'Cargo y Área de Trabajo',
                Row(
                    Column('cargo', css_class='col-md-6'),
                    Column('grupo', css_class='col-md-6'),
                ),
                css_class='mb-4'
            ),

            Fieldset(
                'Permisos Especiales',
                'is_supervisor',
                HTML('''
                <div class="alert alert-info mt-2">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Información:</strong>
                    <ul class="mb-0 mt-2">
                        <li><strong>Cargo:</strong> Posición del empleado (Administrador, Empleado, etc.)</li>
                        <li><strong>Área:</strong> Departamento donde trabajará (Fontanería, Electricidad, etc.)</li>
                        <li><strong>Supervisor:</strong> Si supervisará a otros empleados en su área</li>
                    </ul>
                </div>
                '''),
                css_class='mb-4'
            ),

            FormActions(
                Submit('submit', 'Continuar al Paso 2', css_class='btn btn-primary'),
                HTML('<a href="{% url "user:lista_usuarios" %}" class="btn btn-secondary ms-2">Cancelar</a>'),
                css_class='text-end'
            )
        )

        # Personalizar widgets
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Nombre de usuario'
        })

        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Contraseña'
        })

        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Confirmar contraseña'
        })

    def clean_dpi(self):
        """
        Validación personalizada para el DPI.
        """
        dpi = self.cleaned_data.get('dpi')
        if dpi:
            # Remover espacios y caracteres no numéricos
            dpi = re.sub(r'[^\d]', '', dpi)

            # Verificar que tenga exactamente 13 dígitos
            if len(dpi) != 13:
                raise forms.ValidationError('El DPI debe tener exactamente 13 dígitos.')

            # Verificar que no exista otro usuario con el mismo DPI
            if User.objects.filter(dpi=dpi).exclude(pk=self.instance.pk if self.instance else None).exists():
                raise forms.ValidationError('Ya existe un usuario con este DPI.')

        return dpi

    def clean_username(self):
        """
        Validación personalizada para el nombre de usuario.
        """
        username = self.cleaned_data.get('username')
        if username:
            # Verificar caracteres permitidos
            if not re.match(r'^[a-zA-Z0-9_.-]+$', username):
                raise forms.ValidationError('El nombre de usuario solo puede contener letras, números, puntos, guiones y guiones bajos.')

            # Verificar longitud mínima
            if len(username) < 3:
                raise forms.ValidationError('El nombre de usuario debe tener al menos 3 caracteres.')

        return username


class CelularUsuarioForm(forms.ModelForm):
    """
    Formulario para agregar teléfonos del usuario.
    """

    # Validador para teléfono guatemalteco
    telefono_validator = RegexValidator(
        regex=r'^[0-9+\-\s()]{8,15}$',
        message='Ingrese un número de teléfono válido (8-15 dígitos).'
    )

    numero = forms.CharField(
        max_length=20,
        validators=[telefono_validator],
        label='Número de Teléfono',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '12345678 o +502 1234-5678',
            'pattern': r'[0-9+\-\s()]{8,15}'
        })
    )

    tipo = forms.ChoiceField(
        choices=CelularUsuario.TIPO_CHOICES,
        label='Tipo de Teléfono',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    is_active = forms.ChoiceField(
        choices=[(True, 'Activo'), (False, 'Inactivo')],
        initial=True,
        label='Estado',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    class Meta:
        model = CelularUsuario
        fields = ['numero', 'tipo', 'is_active']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'celular-form'
        self.helper.form_id = 'celular-form'

        self.helper.layout = Layout(
            Row(
                Column('numero', css_class='col-md-8'),
                Column('tipo', css_class='col-md-4'),
            ),
            FormActions(
                Submit('add_celular', 'Agregar Teléfono', css_class='btn btn-success btn-sm'),
                css_class='text-end mt-2'
            )
        )

    def clean_numero(self):
        """
        Validación y normalización del número de teléfono.
        """
        numero = self.cleaned_data.get('numero')
        if numero:
            # Normalizar el número (remover espacios extra, etc.)
            numero = re.sub(r'\s+', ' ', numero.strip())

            # Verificar formato básico
            if not re.match(r'^[0-9+\-\s()]{8,15}$', numero):
                raise forms.ValidationError('Formato de teléfono inválido.')

        return numero


class FamiliarForm(forms.ModelForm):
    """
    Formulario para agregar familiares del usuario.
    """

    nombre = forms.CharField(
        max_length=100,
        label='Nombre Completo del Familiar',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Nombre completo del familiar'
        })
    )

    parentesco = forms.ModelChoiceField(
        queryset=Parentesco.objects.all(),
        label='Parentesco',
        empty_label='Selecciona el parentesco...',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    class Meta:
        model = Familiar
        fields = ['nombre', 'parentesco']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'familiar-form'
        self.helper.form_id = 'familiar-form'

        self.helper.layout = Layout(
            Row(
                Column('nombre', css_class='col-md-8'),
                Column('parentesco', css_class='col-md-4'),
            ),
            FormActions(
                Submit('add_familiar', 'Agregar Familiar', css_class='btn btn-info btn-sm'),
                css_class='text-end mt-2'
            )
        )

    def clean_nombre(self):
        """
        Validación del nombre del familiar.
        """
        nombre = self.cleaned_data.get('nombre')
        if nombre:
            # Verificar que no contenga caracteres especiales peligrosos
            if re.search(r'[<>"\']', nombre):
                raise forms.ValidationError('El nombre no puede contener caracteres especiales como <, >, ", \'')

            # Verificar longitud mínima
            if len(nombre.strip()) < 2:
                raise forms.ValidationError('El nombre debe tener al menos 2 caracteres.')

        return nombre.strip().title()  # Capitalizar nombre


class CelularEmergenciaForm(forms.ModelForm):
    """
    Formulario para agregar teléfonos de emergencia a familiares.
    """

    # Validador para teléfono
    telefono_validator = RegexValidator(
        regex=r'^[0-9+\-\s()]{8,15}$',
        message='Ingrese un número de teléfono válido (8-15 dígitos).'
    )

    numero = forms.CharField(
        max_length=20,
        validators=[telefono_validator],
        label='Teléfono de Emergencia',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '12345678 o +502 1234-5678',
            'pattern': r'[0-9+\-\s()]{8,15}'
        })
    )

    class Meta:
        model = CelularEmergencia
        fields = ['numero']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'celular-emergencia-form'

        self.helper.layout = Layout(
            'numero',
            FormActions(
                Submit('add_celular_emergencia', 'Agregar Teléfono', css_class='btn btn-warning btn-sm'),
                css_class='text-end mt-2'
            )
        )

    def clean_numero(self):
        """
        Validación del número de emergencia.
        """
        numero = self.cleaned_data.get('numero')
        if numero:
            # Normalizar el número
            numero = re.sub(r'\s+', ' ', numero.strip())

            # Verificar formato
            if not re.match(r'^[0-9+\-\s()]{8,15}$', numero):
                raise forms.ValidationError('Formato de teléfono inválido.')

        return numero
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from django.core.exceptions import ValidationError
from django.utils import timezone

from .models import User, CargoUsuario, CelularUsuario, Familiar, CelularEmergencia

class CustomUserCreationForm(UserCreationForm):
    """
    Formulario para crear nuevos usuarios con campos personalizados.
    Extiende UserCreationForm para mantener la funcionalidad de contraseñas.
    """
    class Meta(UserCreationForm.Meta):
        model = User
        fields = ('username', 'first_name', 'last_name', 'email', 'dpi',
                 'fecha_nacimiento', 'genero', 'cargo', 'is_supervisor')
        widgets = {
            'fecha_nacimiento': forms.DateInput(attrs={'type': 'date'}),
            'genero': forms.RadioSelect(),
        }

    def clean_dpi(self):
        """Valida formato del DPI."""
        dpi = self.cleaned_data.get('dpi')
        if not dpi.isdigit() or len(dpi) != 13:  # Asumiendo que DPI tiene 13 dígitos
            raise ValidationError('El DPI debe contener exactamente 13 dígitos numéricos.')
        return dpi

    def clean_fecha_nacimiento(self):
        """Valida que la fecha de nacimiento sea válida y que la persona sea mayor de edad."""
        fecha = self.cleaned_data.get('fecha_nacimiento')
        if fecha:
            hoy = timezone.now().date()
            edad = hoy.year - fecha.year - ((hoy.month, hoy.day) < (fecha.month, fecha.day))
            if edad < 18:
                raise ValidationError('El usuario debe ser mayor de edad.')
            if fecha > hoy:
                raise ValidationError('La fecha de nacimiento no puede ser futura.')
        return fecha

class CustomUserChangeForm(UserChangeForm):
    """
    Formulario para modificar usuarios existentes.
    No incluye cambio de contraseña (se maneja por separado).
    """

    fecha_nacimiento = forms.DateField(
        required=False,
        label='Fecha de Nacimiento',
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-control'
        })
    )

    class Meta:
        model = User
        fields = ('first_name', 'last_name', 'email', 'dpi', 'fecha_nacimiento',
                 'genero', 'cargo', 'is_supervisor', 'is_active')
        widgets = {
            'genero': forms.RadioSelect(),
        }

class CargoUsuarioForm(forms.ModelForm):
    """Formulario para gestionar cargos de usuario."""
    class Meta:
        model = CargoUsuario
        fields = ('nombre', 'descripcion', 'is_active')
        widgets = {
            'descripcion': forms.Textarea(attrs={'rows': 3}),
        }

class CelularUsuarioForm(forms.ModelForm):
    """
    Formulario para números telefónicos de usuarios.
    Incluye validación de formato de número.
    """
    class Meta:
        model = CelularUsuario
        fields = ('numero', 'tipo', 'is_active')

    def clean_numero(self):
        """Valida formato del número telefónico."""
        numero = self.cleaned_data.get('numero')
        if not numero.isdigit() or len(numero) != 8:  # Asumiendo números de 8 dígitos
            raise ValidationError('El número debe contener 8 dígitos numéricos.')
        return numero

class FamiliarForm(forms.ModelForm):
    """
    Formulario para registrar familiares de usuarios.
    Permite agregar múltiples números de emergencia.
    """
    numeros_emergencia = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 2}),
        help_text='Ingrese los números separados por comas',
        required=False
    )

    class Meta:
        model = Familiar
        fields = ('nombre', 'parentesco', 'is_active')

    def clean_numeros_emergencia(self):
        """Valida formato de números de emergencia."""
        numeros = self.cleaned_data.get('numeros_emergencia')
        if numeros:
            numeros_list = [num.strip() for num in numeros.split(',')]
            for numero in numeros_list:
                if not numero.isdigit() or len(numero) != 8:
                    raise ValidationError(
                        f'El número {numero} no es válido. Debe contener 8 dígitos.'
                    )
            return numeros_list
        return []

    def save(self, commit=True):
        """
        Sobrescribe el método save para manejar la creación
        de números de emergencia asociados.
        """
        familiar = super().save(commit=commit)
        if commit:
            # Elimina números existentes si los hay
            familiar.celulares_emergencia.all().delete()
            # Crea nuevos números de emergencia
            numeros = self.cleaned_data.get('numeros_emergencia', [])
            for numero in numeros:
                CelularEmergencia.objects.create(
                    familiar=familiar,
                    numero=numero
                )
        return familiar

class CelularEmergenciaForm(forms.ModelForm):
    """Formulario para números de emergencia individuales."""
    class Meta:
        model = CelularEmergencia
        fields = ('numero', 'is_active')

    def clean_numero(self):
        """Valida formato del número de emergencia."""
        numero = self.cleaned_data.get('numero')
        if not numero.isdigit() or len(numero) != 8:
            raise ValidationError('El número debe contener 8 dígitos numéricos.')
        return numero