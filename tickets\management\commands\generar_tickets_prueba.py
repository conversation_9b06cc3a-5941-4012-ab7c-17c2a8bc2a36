"""
Comando de Django para generar tickets de prueba con ciudadanos asociados.

Uso:
    python manage.py generar_tickets_prueba --cantidad 250

Este comando crea:
- Ciudadanos aleatorios con DPIs únicos
- Tickets realistas con diferentes prioridades y estados
- Asociaciones ciudadano-ticket
- Distribución realista entre diferentes áreas
"""

import random
from datetime import date, datetime, timedelta
from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction

from tickets.models import Ticket
from ciudadano.models import Ciudadano, CiudadanoTicket

User = get_user_model()


class Command(BaseCommand):
    help = 'Genera tickets de prueba con ciudadanos asociados para testing de rendimiento'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cantidad',
            type=int,
            default=250,
            help='Cantidad de tickets a generar (default: 250)'
        )
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Eliminar tickets y ciudadanos existentes antes de generar nuevos'
        )

    def handle(self, *args, **options):
        cantidad = options['cantidad']
        reset = options['reset']

        self.stdout.write(
            self.style.SUCCESS(f'🎫 Generando {cantidad} tickets de prueba...')
        )

        if reset:
            self.limpiar_datos_existentes()

        # Verificar que existan usuarios y grupos
        usuarios = list(User.objects.filter(is_active=True))
        grupos = list(Group.objects.all())

        if not usuarios:
            self.stdout.write(
                self.style.ERROR('❌ No hay usuarios activos. Ejecuta: python manage.py crear_usuarios_prueba')
            )
            return

        if not grupos:
            self.stdout.write(
                self.style.ERROR('❌ No hay grupos/áreas. Ejecuta: python manage.py crear_grupos_areas')
            )
            return

        self.stdout.write(f'📊 Usuarios disponibles: {len(usuarios)}')
        self.stdout.write(f'📊 Grupos/áreas disponibles: {len(grupos)}')

        # Generar datos
        ciudadanos_creados = 0
        tickets_creados = 0

        # Datos para generar contenido realista
        tipos_problemas = [
            "Fuga de agua", "Problema eléctrico", "Bache en la calle", "Alumbrado público",
            "Recolección de basura", "Limpieza de alcantarillas", "Poda de árboles",
            "Reparación de acera", "Semáforo dañado", "Señalización vial",
            "Mantenimiento de parque", "Limpieza de lote baldío", "Ruido excesivo",
            "Animales callejeros", "Daño en tubería", "Corte de energía",
            "Problema de drenaje", "Vandalismo", "Iluminación deficiente",
            "Reparación de puente", "Limpieza de cunetas", "Mantenimiento de jardines"
        ]

        direcciones_base = [
            "Zona 1, Ciudad de Guatemala", "Zona 10, Ciudad de Guatemala",
            "Zona 4, Mixco", "Zona 7, Villa Nueva", "Zona 2, Petapa",
            "Zona 5, San Miguel Petapa", "Zona 3, Villa Canales",
            "Zona 6, Amatitlán", "Zona 8, Chinautla", "Zona 9, Santa Catarina Pinula"
        ]

        nombres_ciudadanos = [
            "María García López", "Juan Carlos Pérez", "Ana Sofía Rodríguez",
            "Luis Fernando Morales", "Carmen Elena Vásquez", "José Antonio Herrera",
            "Claudia Patricia Méndez", "Roberto Carlos Jiménez", "Silvia Esperanza Cruz",
            "Miguel Ángel Castillo", "Rosa María Flores", "Carlos Eduardo Ramírez"
        ]

        with transaction.atomic():
            for i in range(cantidad):
                try:
                    # Crear ciudadano
                    ciudadano = self.crear_ciudadano_aleatorio(nombres_ciudadanos)
                    if ciudadano:
                        ciudadanos_creados += 1

                    # Crear ticket
                    ticket = self.crear_ticket_aleatorio(
                        usuarios, grupos, tipos_problemas, direcciones_base
                    )
                    if ticket:
                        tickets_creados += 1

                        # Asociar ciudadano con ticket (80% de probabilidad)
                        if ciudadano and random.random() < 0.8:
                            CiudadanoTicket.objects.create(
                                ciudadano=ciudadano,
                                ticket=ticket,
                                observaciones=f"Solicitud generada automáticamente para testing"
                            )

                    # Mostrar progreso cada 50 tickets
                    if (i + 1) % 50 == 0:
                        self.stdout.write(f'📈 Progreso: {i + 1}/{cantidad} tickets creados...')

                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'⚠️ Error creando ticket {i + 1}: {str(e)}')
                    )
                    continue

        # Estadísticas finales
        self.mostrar_estadisticas_finales(ciudadanos_creados, tickets_creados)

    def limpiar_datos_existentes(self):
        """Elimina tickets y ciudadanos existentes."""
        self.stdout.write('🧹 Limpiando datos existentes...')
        
        tickets_eliminados = Ticket.objects.all().delete()[0]
        ciudadanos_eliminados = Ciudadano.objects.all().delete()[0]
        
        self.stdout.write(f'   - Tickets eliminados: {tickets_eliminados}')
        self.stdout.write(f'   - Ciudadanos eliminados: {ciudadanos_eliminados}')

    def crear_ciudadano_aleatorio(self, nombres_base):
        """Crea un ciudadano con datos aleatorios."""
        try:
            dpi = self.generar_dpi_unico_ciudadano()
            nombre = random.choice(nombres_base)
            
            # Agregar variación al nombre
            sufijos = ["", " Jr.", " II", " III"]
            nombre_completo = nombre + random.choice(sufijos)
            
            ciudadano = Ciudadano.objects.create(
                dpi=dpi,
                nombre_completo=nombre_completo,
                direccion=f"{random.randint(1, 50)} Calle {random.randint(1, 20)}-{random.randint(10, 99)}, Zona {random.randint(1, 25)}",
                telefono=self.generar_telefono(),
                email=f"{nombre.lower().replace(' ', '.')}@email.com" if random.random() < 0.6 else "",
                fecha_nacimiento=self.generar_fecha_nacimiento(),
                genero=random.choice([1, 2])
            )
            return ciudadano
        except Exception as e:
            self.stdout.write(f'Error creando ciudadano: {str(e)}')
            return None

    def crear_ticket_aleatorio(self, usuarios, grupos, tipos_problemas, direcciones_base):
        """Crea un ticket con datos aleatorios."""
        try:
            problema = random.choice(tipos_problemas)
            
            # Generar título y descripción realistas
            titulo = f"{problema} en {random.choice(direcciones_base)}"
            
            descripciones = [
                f"Se reporta {problema.lower()} que requiere atención inmediata. Los vecinos han manifestado su preocupación.",
                f"Solicito atención para {problema.lower()} que está afectando a la comunidad desde hace varios días.",
                f"Es urgente resolver el problema de {problema.lower()} ya que está causando molestias a los residentes.",
                f"Por favor revisar {problema.lower()} que se encuentra en mal estado y necesita reparación.",
                f"Se requiere intervención municipal para {problema.lower()} que está generando inconvenientes."
            ]
            
            descripcion = random.choice(descripciones)
            
            # Distribución realista de prioridades
            prioridades_peso = [
                ('baja', 40),      # 40%
                ('media', 35),     # 35%
                ('alta', 20),      # 20%
                ('critica', 5)     # 5%
            ]
            prioridad = self.elegir_con_peso(prioridades_peso)
            
            # Distribución realista de estados
            estados_peso = [
                (1, 60),  # Abierto - 60%
                (2, 25),  # En Progreso - 25%
                (3, 10),  # Cerrado - 10%
                (4, 5)    # Pendiente - 5%
            ]
            estado = self.elegir_con_peso(estados_peso)
            
            # Fecha de creación aleatoria (últimos 6 meses)
            fecha_inicio = timezone.now() - timedelta(days=180)
            fecha_creacion = fecha_inicio + timedelta(
                seconds=random.randint(0, int((timezone.now() - fecha_inicio).total_seconds()))
            )
            
            ticket = Ticket.objects.create(
                titulo=titulo[:200],  # Respetar límite de caracteres
                descripcion=descripcion,
                estado=estado,
                prioridad=prioridad,
                creado_por=random.choice(usuarios),
                grupo=random.choice(grupos),
                direccion=f"{random.choice(direcciones_base)}, {random.randint(1, 100)} Avenida",
                fecha_creacion=fecha_creacion
            )
            
            # Si el ticket está cerrado, agregar fecha de finalización
            if estado == 3:  # Cerrado
                ticket.fecha_finalizacion = fecha_creacion + timedelta(
                    days=random.randint(1, 30)
                )
                ticket.save()
            
            return ticket
            
        except Exception as e:
            self.stdout.write(f'Error creando ticket: {str(e)}')
            return None

    def generar_dpi_unico_ciudadano(self):
        """Genera un DPI único para ciudadano."""
        max_intentos = 100
        for _ in range(max_intentos):
            dpi = ''.join([str(random.randint(0, 9)) for _ in range(13)])
            if not Ciudadano.objects.filter(dpi=dpi).exists():
                return dpi
        raise Exception("No se pudo generar DPI único después de 100 intentos")

    def generar_telefono(self):
        """Genera un número de teléfono guatemalteco."""
        return f"{random.choice(['2', '3', '4', '5'])}{random.randint(100, 999)}-{random.randint(1000, 9999)}"

    def generar_fecha_nacimiento(self):
        """Genera una fecha de nacimiento realista."""
        año_inicio = 1950
        año_fin = 2005
        año = random.randint(año_inicio, año_fin)
        mes = random.randint(1, 12)
        día = random.randint(1, 28)  # Usar 28 para evitar problemas con febrero
        return date(año, mes, día)

    def elegir_con_peso(self, opciones_peso):
        """Elige una opción basada en pesos."""
        opciones, pesos = zip(*opciones_peso)
        return random.choices(opciones, weights=pesos)[0]

    def mostrar_estadisticas_finales(self, ciudadanos_creados, tickets_creados):
        """Muestra estadísticas finales del proceso."""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(
            self.style.SUCCESS('✅ ¡Generación de datos completada!')
        )
        self.stdout.write(f'📊 Ciudadanos creados: {ciudadanos_creados}')
        self.stdout.write(f'🎫 Tickets creados: {tickets_creados}')
        
        # Estadísticas adicionales
        total_tickets = Ticket.objects.count()
        total_ciudadanos = Ciudadano.objects.count()
        tickets_con_ciudadano = CiudadanoTicket.objects.count()
        
        self.stdout.write(f'\n📈 Estadísticas del sistema:')
        self.stdout.write(f'   - Total tickets: {total_tickets}')
        self.stdout.write(f'   - Total ciudadanos: {total_ciudadanos}')
        self.stdout.write(f'   - Tickets con ciudadano: {tickets_con_ciudadano}')
        
        # Distribución por estado
        estados = Ticket.objects.values_list('estado', flat=True)
        for estado_num, estado_nombre in Ticket.ESTADO_CHOICES:
            count = list(estados).count(estado_num)
            porcentaje = (count / total_tickets * 100) if total_tickets > 0 else 0
            self.stdout.write(f'   - {estado_nombre}: {count} ({porcentaje:.1f}%)')
        
        self.stdout.write(
            self.style.SUCCESS('\n🚀 ¡Listo para probar el rendimiento del sistema!')
        )
